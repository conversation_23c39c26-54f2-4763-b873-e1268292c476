"use strict";
const utils_request = require("../utils/request.js");
function getOrderDetail(id) {
  return utils_request.request({
    url: `/order/${id}`,
    method: "GET"
  });
}
function updateOrderStatus(id, status, handleRemark) {
  return utils_request.request({
    url: `/order/${id}/status`,
    method: "PUT",
    data: { status, handleRemark }
  });
}
exports.getOrderDetail = getOrderDetail;
exports.updateOrderStatus = updateOrderStatus;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/order.js.map
