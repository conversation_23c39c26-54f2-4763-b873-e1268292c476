<template>
  <view class="login-container">
    <view class="header">
      <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
      <text class="title">欢迎登录</text>
    </view>
    
    <view class="form">
      <!-- 登录方式切换 -->
      <view class="login-type">
        <text 
          :class="['type-item', loginType === 'phone' ? 'active' : '']" 
          @click="loginType = 'phone'"
        >手机号登录</text>
        <text 
          :class="['type-item', loginType === 'password' ? 'active' : '']" 
          @click="loginType = 'password'"
        >密码登录</text>
      </view>
      
      <!-- 手机号登录 -->
      <view v-if="loginType === 'phone'">
        <view class="input-group">
          <text class="label">手机号</text>
          <input 
            type="number" 
            v-model="phone" 
            placeholder="请输入手机号"
            maxlength="11"
          />
        </view>
        <view class="input-group">
          <text class="label">验证码</text>
          <input 
            type="number" 
            v-model="code" 
            placeholder="请输入验证码"
            maxlength="6"
          />
          <text 
            class="send-code" 
            :class="{ disabled: counting }"
            @click="sendCode"
          >{{ counting ? `${countdown}s后重发` : '发送验证码' }}</text>
        </view>
      </view>
      
      <!-- 密码登录 -->
      <view v-else>
        <view class="input-group">
          <text class="label">手机号</text>
          <input 
            type="number" 
            v-model="phone" 
            placeholder="请输入手机号"
            maxlength="11"
          />
        </view>
        <view class="input-group">
          <text class="label">密码</text>
          <input 
            :type="showPassword ? 'text' : 'password'" 
            v-model="password" 
            placeholder="请输入密码"
          />
          <text 
            class="toggle-password" 
            @click="showPassword = !showPassword"
          >{{ showPassword ? '隐藏' : '显示' }}</text>
        </view>
      </view>
      
      <!-- 登录按钮 -->
      <button 
        class="login-btn" 
        :disabled="!canLogin"
        @click="handleLogin"
      >登录</button>
      
      <!-- 其他操作 -->
      <view class="other-options">
        <text @click="navigateTo('/pages/register/index')">注册账号</text>
        <text @click="navigateTo('/pages/forgot-password/index')">忘记密码</text>
      </view>
    </view>
  </view>
</template>

<script>
import { login, getCurrentUser } from '@/api/user'

export default {
  data() {
    return {
      loginType: 'phone', // phone 或 password
      phone: '',
      code: '',
      password: '',
      showPassword: false,
      counting: false,
      countdown: 60
    }
  },
  computed: {
    canLogin() {
      if (this.loginType === 'phone') {
        return this.phone.length === 11 && this.code.length === 6
      } else {
        return this.phone.length === 11 && this.password.length >= 6
      }
    }
  },
  methods: {
    async sendCode() {
      if (this.counting) return
      if (this.phone.length !== 11) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }
      
      try {
        await sendCode(this.phone)
        this.counting = true
        this.countdown = 60
        const timer = setInterval(() => {
          this.countdown--
          if (this.countdown <= 0) {
            clearInterval(timer)
            this.counting = false
          }
        }, 1000)
        
        uni.showToast({
          title: '验证码已发送',
          icon: 'success'
        })
      } catch (error) {
        uni.showToast({
          title: '发送失败，请重试',
          icon: 'none'
        })
      }
    },
    async handleLogin() {
      if (!this.canLogin) return
      
      try {
        const res = await login({
          phone: this.phone,
          password: this.password
        })
        
        // 保存token
        uni.setStorageSync('token', res)
        
        // 获取用户信息
        const userInfo = await getCurrentUser()
        uni.setStorageSync('userInfo', userInfo)
        
        uni.showToast({
          title: '登录成功',
          icon: 'success'
        })
        
        // 跳转到首页
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/index/index'
          })
        }, 1500)
      } catch (error) {
        uni.showToast({
          title: error.message || '登录失败，请重试',
          icon: 'none'
        })
      }
    },
    navigateTo(url) {
      uni.navigateTo({ url })
    }
  }
}
</script>

<style lang="scss">
.login-container {
  min-height: 100vh;
  padding: 40rpx;
  background-color: #fff;
  
  .header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 60rpx 0;
    
    .logo {
      width: 160rpx;
      height: 160rpx;
      margin-bottom: 20rpx;
    }
    
    .title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }
  }
  
  .form {
    .login-type {
      display: flex;
      margin-bottom: 40rpx;
      
      .type-item {
        flex: 1;
        text-align: center;
        font-size: 32rpx;
        color: #666;
        padding: 20rpx 0;
        position: relative;
        
        &.active {
          color: #2979ff;
          font-weight: bold;
          
          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 40rpx;
            height: 4rpx;
            background-color: #2979ff;
            border-radius: 2rpx;
          }
        }
      }
    }
    
    .input-group {
      position: relative;
      margin-bottom: 30rpx;
      
      .label {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 10rpx;
        display: block;
      }
      
      input {
        width: 100%;
        height: 88rpx;
        background-color: #f5f5f5;
        border-radius: 44rpx;
        padding: 0 40rpx;
        font-size: 28rpx;
      }
      
      .send-code {
        position: absolute;
        right: 40rpx;
        top: 50%;
        transform: translateY(-50%);
        font-size: 28rpx;
        color: #2979ff;
        
        &.disabled {
          color: #999;
        }
      }
      
      .toggle-password {
        position: absolute;
        right: 40rpx;
        top: 50%;
        transform: translateY(-50%);
        font-size: 28rpx;
        color: #666;
      }
    }
    
    .login-btn {
      width: 100%;
      height: 88rpx;
      background-color: #2979ff;
      color: #fff;
      font-size: 32rpx;
      border-radius: 44rpx;
      margin-top: 60rpx;
      
      &[disabled] {
        background-color: #ccc;
      }
    }
    
    .other-options {
      display: flex;
      justify-content: space-between;
      margin-top: 30rpx;
      padding: 0 20rpx;
      
      text {
        font-size: 28rpx;
        color: #666;
      }
    }
  }
}
</style> 