"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      userInfo: {
        id: null,
        username: "",
        avatar: "",
        level: ""
      }
    };
  },
  onShow() {
    this.getUserInfo();
  },
  methods: {
    getUserInfo() {
      this.userInfo = {
        id: 1,
        username: "张三",
        avatar: "/static/default/avatar.svg",
        level: "黄金会员"
      };
    },
    goToLogin() {
      common_vendor.index.__f__("log", "at pages/user/index.vue:76", "跳转到登录页面");
    },
    goToProfile() {
      common_vendor.index.__f__("log", "at pages/user/index.vue:80", "跳转到个人信息页面");
    },
    goToOrders() {
      common_vendor.index.__f__("log", "at pages/user/index.vue:84", "跳转到订单页面");
    },
    goToAddress() {
      common_vendor.index.__f__("log", "at pages/user/index.vue:88", "跳转到地址页面");
    },
    goToAfterSales() {
      common_vendor.index.navigateTo({
        url: "/pages/after-sales/index"
      });
    },
    contactService() {
      common_vendor.index.makePhoneCall({
        phoneNumber: "************"
      });
    }
  }
};
if (!Array) {
  const _component_u_icon = common_vendor.resolveComponent("u-icon");
  _component_u_icon();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.userInfo.id
  }, $data.userInfo.id ? common_vendor.e({
    b: $data.userInfo.avatar,
    c: common_vendor.t($data.userInfo.username),
    d: $data.userInfo.level
  }, $data.userInfo.level ? {
    e: common_vendor.t($data.userInfo.level)
  } : {}) : {
    f: common_assets._imports_0,
    g: common_vendor.o((...args) => $options.goToLogin && $options.goToLogin(...args))
  }, {
    h: common_vendor.p({
      name: "account",
      size: "40",
      color: "#2979ff"
    }),
    i: common_vendor.p({
      name: "arrow-right",
      size: "30",
      color: "#999"
    }),
    j: common_vendor.o((...args) => $options.goToProfile && $options.goToProfile(...args)),
    k: common_vendor.p({
      name: "order",
      size: "40",
      color: "#2979ff"
    }),
    l: common_vendor.p({
      name: "arrow-right",
      size: "30",
      color: "#999"
    }),
    m: common_vendor.o((...args) => $options.goToOrders && $options.goToOrders(...args)),
    n: common_vendor.p({
      name: "map",
      size: "40",
      color: "#2979ff"
    }),
    o: common_vendor.p({
      name: "arrow-right",
      size: "30",
      color: "#999"
    }),
    p: common_vendor.o((...args) => $options.goToAddress && $options.goToAddress(...args)),
    q: common_vendor.p({
      name: "server-man",
      size: "40",
      color: "#2979ff"
    }),
    r: common_vendor.p({
      name: "arrow-right",
      size: "30",
      color: "#999"
    }),
    s: common_vendor.o((...args) => $options.goToAfterSales && $options.goToAfterSales(...args)),
    t: common_vendor.p({
      name: "phone",
      size: "40",
      color: "#2979ff"
    }),
    v: common_vendor.p({
      name: "arrow-right",
      size: "30",
      color: "#999"
    }),
    w: common_vendor.o((...args) => $options.contactService && $options.contactService(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user/index.js.map
