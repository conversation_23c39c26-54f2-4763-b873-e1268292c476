{"version": 3, "file": "category.js", "sources": ["api/category.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取分类列表\r\nexport function getCategoryList() {\r\n  return request({\r\n    url: '/category',\r\n    method: 'GET'\r\n  })\r\n}\r\n\r\n// 获取分类详情\r\nexport function getCategory(id) {\r\n  return request({\r\n    url: `/category/${id}`,\r\n    method: 'GET'\r\n  })\r\n}\r\n\r\n// 创建分类\r\nexport function createCategory(data) {\r\n  return request({\r\n    url: '/category',\r\n    method: 'POST',\r\n    data\r\n  })\r\n}\r\n\r\n// 更新分类\r\nexport function updateCategory(id, data) {\r\n  return request({\r\n    url: `/category/${id}`,\r\n    method: 'PUT',\r\n    data\r\n  })\r\n}\r\n\r\n// 删除分类\r\nexport function deleteCategory(id) {\r\n  return request({\r\n    url: `/category/${id}`,\r\n    method: 'DELETE'\r\n  })\r\n}\r\n\r\n// 更新分类状态\r\nexport function updateCategoryStatus(id, enabled) {\r\n  return request({\r\n    url: `/category/${id}/status`,\r\n    method: 'PUT',\r\n    data: { enabled }\r\n  })\r\n} "], "names": ["request"], "mappings": ";;AAGO,SAAS,kBAAkB;AAChC,SAAOA,sBAAQ;AAAA,IACb,KAAK;AAAA,IACL,QAAQ;AAAA,EACZ,CAAG;AACH;AAGO,SAAS,YAAY,IAAI;AAC9B,SAAOA,sBAAQ;AAAA,IACb,KAAK,aAAa,EAAE;AAAA,IACpB,QAAQ;AAAA,EACZ,CAAG;AACH;AAGO,SAAS,eAAe,MAAM;AACnC,SAAOA,sBAAQ;AAAA,IACb,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACJ,CAAG;AACH;AAGO,SAAS,eAAe,IAAI,MAAM;AACvC,SAAOA,sBAAQ;AAAA,IACb,KAAK,aAAa,EAAE;AAAA,IACpB,QAAQ;AAAA,IACR;AAAA,EACJ,CAAG;AACH;;;;;"}