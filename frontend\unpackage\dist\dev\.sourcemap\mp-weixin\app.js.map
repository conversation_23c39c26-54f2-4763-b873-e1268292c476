{"version": 3, "file": "app.js", "sources": ["App.vue"], "sourcesContent": ["<script>\r\n\texport default {\r\n\t\tonLaunch: function() {\r\n\t\t\tconsole.log('App Launch')\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\tconsole.log('App Show')\r\n\t\t},\r\n\t\tonHide: function() {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/*每个页面公共css */\r\n\tpage {\r\n\t\tbackground-color: #f8f8f8;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica,\r\n\t\t\tSegoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei',\r\n\t\t\tsans-serif;\r\n\t}\r\n\r\n\t.container {\r\n\t\tpadding: 20rpx;\r\n\t}\r\n\r\n\t.card {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 20rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);\r\n\t}\r\n</style>\r\n"], "names": ["uni"], "mappings": ";;;;;;;;;;;;;;;;;;AACC,MAAK,YAAU;AAAA,EACd,UAAU,WAAW;AACpBA,kBAAAA,MAAA,MAAA,OAAA,gBAAY,YAAY;AAAA,EACxB;AAAA,EACD,QAAQ,WAAW;AAClBA,kBAAAA,MAAY,MAAA,OAAA,gBAAA,UAAU;AAAA,EACtB;AAAA,EACD,QAAQ,WAAW;AAClBA,kBAAAA,MAAY,MAAA,OAAA,iBAAA,UAAU;AAAA,EACvB;AACD;;;;;;;;;"}