<template>
  <view class="container">
    <view class="header">
      <view class="search-box">
        <input 
          type="text" 
          v-model="searchKeyword" 
          placeholder="搜索分类" 
          class="search-input"
          @confirm="handleSearch"
        />
        <button class="search-btn" @tap="handleSearch">搜索</button>
      </view>
      <button class="add-btn" @tap="handleAdd">添加分类</button>
    </view>

    <view class="category-list">
      <view class="category-item" v-for="(item, index) in categories" :key="index">
        <view class="category-info">
          <text class="category-name">{{ item.name }}</text>
          <text class="category-desc">{{ item.description }}</text>
        </view>
        <view class="category-actions">
          <button class="action-btn edit" @tap="handleEdit(item.id)">编辑</button>
          <button class="action-btn delete" @tap="handleDelete(item.id)">删除</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getCategories, deleteCategory } from '@/api/admin'

export default {
  data() {
    return {
      searchKeyword: '',
      categories: []
    }
  },
  onLoad() {
    this.loadCategories()
  },
  methods: {
    loadCategories() {
      getCategories({
        keyword: this.searchKeyword
      }).then(res => {
        this.categories = res.data
      })
    },
    handleSearch() {
      this.loadCategories()
    },
    handleAdd() {
      uni.navigateTo({
        url: '/pages/admin/category/edit'
      })
    },
    handleEdit(id) {
      uni.navigateTo({
        url: `/pages/admin/category/edit?id=${id}`
      })
    },
    handleDelete(id) {
      uni.showModal({
        title: '提示',
        content: '确定要删除该分类吗？',
        success: (res) => {
          if (res.confirm) {
            deleteCategory(id).then(() => {
              uni.showToast({
                title: '删除成功'
              })
              this.loadCategories()
            })
          }
        }
      })
    }
  }
}
</script>

<style>
.container {
  padding: 20rpx;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.search-box {
  display: flex;
  flex: 1;
  margin-right: 20rpx;
}

.search-input {
  flex: 1;
  height: 72rpx;
  background: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
}

.search-btn {
  width: 120rpx;
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  background: #2979ff;
  color: #fff;
  border-radius: 36rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
}

.add-btn {
  width: 160rpx;
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  background: #19be6b;
  color: #fff;
  border-radius: 36rpx;
  font-size: 28rpx;
}

.category-list {
  display: flex;
  flex-direction: column;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.category-info {
  flex: 1;
}

.category-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.category-desc {
  font-size: 26rpx;
  color: #666;
}

.category-actions {
  display: flex;
  flex-direction: column;
  margin-left: 20rpx;
}

.action-btn {
  width: 120rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  border-radius: 30rpx;
  font-size: 24rpx;
  margin-bottom: 10rpx;
}

.edit {
  background: #2979ff;
  color: #fff;
}

.delete {
  background: #ff6b6b;
  color: #fff;
}
</style> 