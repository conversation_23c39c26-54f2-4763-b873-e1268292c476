<template>
  <view class="admin-container">
    <view class="header">
      <view class="title">后台管理</view>
      <view class="user-info">
        <text>{{ userInfo.realName }}</text>
        <text class="logout" @click="handleLogout">退出</text>
      </view>
    </view>
    
    <view class="menu-list">
      <view class="menu-item" @click="navigateTo('/pages/admin/product/list')">
        <text class="icon">📦</text>
        <text>产品管理</text>
      </view>
      <view class="menu-item" @click="navigateTo('/pages/admin/category/list')">
        <text class="icon">📑</text>
        <text>分类管理</text>
      </view>
    </view>
  </view>
</template>

<script>
import { getCurrentUser } from '@/api/user'

export default {
  data() {
    return {
      userInfo: {}
    }
  },
  onLoad() {
    this.getUserInfo()
  },
  methods: {
    async getUserInfo() {
      try {
        this.userInfo = await getCurrentUser()
      } catch (error) {
        console.error('获取用户信息失败:', error)
        uni.showToast({
          title: '请先登录',
          icon: 'none'
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    },
    navigateTo(url) {
      uni.navigateTo({ url })
    },
    async handleLogout() {
      try {
        uni.removeStorageSync('token')
        uni.removeStorageSync('userInfo')
        uni.reLaunch({
          url: '/pages/login/index'
        })
      } catch (error) {
        console.error('退出登录失败:', error)
      }
    }
  }
}
</script>

<style lang="scss">
.admin-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  
  .header {
    background-color: #2979ff;
    padding: 40rpx;
    color: #fff;
    
    .title {
      font-size: 36rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
    }
    
    .user-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 28rpx;
      
      .logout {
        color: #fff;
        text-decoration: underline;
      }
    }
  }
  
  .menu-list {
    padding: 30rpx;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30rpx;
    
    .menu-item {
      background-color: #fff;
      padding: 40rpx;
      border-radius: 12rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
      
      .icon {
        font-size: 48rpx;
        margin-bottom: 20rpx;
      }
      
      text {
        font-size: 28rpx;
        color: #333;
      }
    }
  }
}
</style> 