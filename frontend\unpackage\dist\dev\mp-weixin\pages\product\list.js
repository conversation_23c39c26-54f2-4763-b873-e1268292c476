"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      searchKeyword: "",
      selectedCategory: "all",
      categories: [
        { id: "all", name: "全部" },
        { id: "camera", name: "监控" },
        { id: "lock", name: "门锁" },
        { id: "router", name: "路由器" }
      ],
      products: [
        {
          id: 1,
          name: "智能监控摄像头",
          description: "1080P高清画质，360°全景监控",
          price: 299,
          unit: "台",
          category: "camera",
          image: "/static/product/camera.svg"
        },
        {
          id: 2,
          name: "智能指纹锁",
          description: "指纹识别，密码解锁，远程控制",
          price: 999,
          unit: "套",
          category: "lock",
          image: "/static/product/lock.svg"
        },
        {
          id: 3,
          name: "WiFi6路由器",
          description: "双频千兆，信号增强",
          price: 399,
          unit: "台",
          category: "router",
          image: "/static/product/router.svg"
        }
      ]
    };
  },
  computed: {
    filteredProducts() {
      let result = this.products;
      if (this.selectedCategory !== "all") {
        result = result.filter((item) => item.category === this.selectedCategory);
      }
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase();
        result = result.filter(
          (item) => item.name.toLowerCase().includes(keyword) || item.description.toLowerCase().includes(keyword)
        );
      }
      return result;
    }
  },
  methods: {
    handleSearch() {
    },
    selectCategory(categoryId) {
      this.selectedCategory = categoryId;
    },
    goToDetail(id) {
      common_vendor.index.navigateTo({
        url: `/pages/product/detail?id=${id}`
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    b: $data.searchKeyword,
    c: common_vendor.o(($event) => $data.searchKeyword = $event.detail.value),
    d: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    e: common_vendor.f($data.categories, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: index,
        c: common_vendor.n($data.selectedCategory === item.id ? "active" : ""),
        d: common_vendor.o(($event) => $options.selectCategory(item.id), index)
      };
    }),
    f: common_vendor.f($options.filteredProducts, (item, index, i0) => {
      return {
        a: item.image,
        b: common_vendor.t(item.name),
        c: common_vendor.t(item.description),
        d: common_vendor.t(item.price),
        e: common_vendor.t(item.unit),
        f: index,
        g: common_vendor.o(($event) => $options.goToDetail(item.id), index)
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/product/list.js.map
