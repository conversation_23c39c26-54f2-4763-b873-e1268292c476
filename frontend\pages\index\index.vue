<template>
	<view class="container">
		<!-- 轮播图 -->
		<swiper class="banner" circular :indicator-dots="true" :autoplay="true" :interval="3000" :duration="1000">
			<swiper-item v-for="(item, index) in banners" :key="index" @tap="goToDetail(item.link)">
				<image :src="item.image" mode="aspectFill" class="banner-image"/>
			</swiper-item>
		</swiper>

		<!-- 产品分类 -->
		<view class="category-section">
			<view class="section-title">产品分类</view>
			<view class="category-grid">
				<view class="category-item" v-for="(item, index) in categories" :key="index" @tap="goToCategory(item.id)">
					<image :src="item.icon || '/static/category/default.png'" mode="aspectFit" class="category-icon"/>
					<text class="category-name">{{ item.name }}</text>
				</view>
			</view>
		</view>

		<!-- 推荐产品 -->
		<view class="recommend-section">
			<view class="section-title">推荐产品</view>
			<view class="product-list">
				<view class="product-item" v-for="(item, index) in recommendProducts" :key="index" @tap="goToDetail(item.id)">
					<image :src="item.mainImage" mode="aspectFill" class="product-image"/>
					<view class="product-info">
						<text class="product-name">{{ item.name }}</text>
						<text class="product-desc">{{ item.description }}</text>
						<view class="product-price">
							<text class="price">¥{{ item.price }}</text>
							<text class="unit">/台</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { getBanners, getCategories, getRecommendProducts } from '@/api/home'

export default {
	data() {
		return {
			banners: [],
			categories: [],
			recommendProducts: []
		}
	},
	onLoad() {
		this.loadData()
	},
	methods: {
		async loadData() {
			try {
				// 获取轮播图数据
				const bannersRes = await getBanners()
				this.banners = bannersRes || []

				// 获取分类数据
				const categoriesRes = await getCategories()
				this.categories = categoriesRes || []

				// 获取推荐产品
				const productsRes = await getRecommendProducts()
				this.recommendProducts = productsRes || []
			} catch (error) {
				console.error('加载数据失败:', error)
				uni.showToast({
					title: '加载数据失败',
					icon: 'none'
				})
			}
		},
		goToCategory(categoryId) {
			uni.navigateTo({
				url: `/pages/product/list?category=${categoryId}`
			})
		},
		goToDetail(id) {
			uni.navigateTo({
				url: `/pages/product/detail?id=${id}`
			})
		}
	}
}
</script>

<style>
	.container {
		padding: 20rpx;
	}

	.banner {
		height: 300rpx;
		border-radius: 12rpx;
		overflow: hidden;
		margin-bottom: 30rpx;
	}

	.banner-image {
		width: 100%;
		height: 100%;
	}

	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
	}

	.category-section {
		margin-bottom: 30rpx;
	}

	.category-grid {
		display: flex;
		flex-wrap: wrap;
		background: #fff;
		border-radius: 12rpx;
		padding: 20rpx;
	}

	.category-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 25%;
		margin-bottom: 20rpx;
	}

	.category-icon {
		width: 80rpx;
		height: 80rpx;
		margin-bottom: 10rpx;
	}

	.category-name {
		font-size: 26rpx;
		color: #666;
	}

	.recommend-section {
		margin-bottom: 30rpx;
	}

	.product-list {
		display: flex;
		flex-direction: column;
	}

	.product-item {
		display: flex;
		background: #fff;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
	}

	.product-image {
		width: 200rpx;
		height: 200rpx;
		border-radius: 8rpx;
		margin-right: 20rpx;
	}

	.product-info {
		flex: 1;
		display: flex;
		flex-direction: column;
	}

	.product-name {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 10rpx;
	}

	.product-desc {
		font-size: 26rpx;
		color: #666;
		margin-bottom: 20rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	.product-price {
		margin-top: auto;
	}

	.price {
		font-size: 36rpx;
		color: #ff6b6b;
		font-weight: bold;
	}

	.unit {
		font-size: 24rpx;
		color: #999;
		margin-left: 4rpx;
	}
</style>
