package com.baojia.service;

import com.baojia.entity.User;
import com.baojia.model.LoginRequest;

public interface UserService {
    /**
     * 用户登录
     * @param request 登录请求
     * @return JWT token
     */
    String login(LoginRequest request);

    /**
     * 用户注册
     * @param user 用户信息
     * @return 注册后的用户信息
     */
    User register(User user);

    /**
     * 发送验证码
     * @param phone 手机号
     */
    void sendCode(String phone);

    /**
     * 获取当前登录用户
     * @return 用户信息
     */
    User getCurrentUser();

    /**
     * 更新当前用户信息
     * @param user 用户信息
     * @return 更新后的用户信息
     */
    User updateCurrentUser(User user);
} 