/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container {
  padding: 20rpx;
}
.user-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}
.user-card .user-info {
  display: flex;
  align-items: center;
}
.user-card .user-info .avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 20rpx;
}
.user-card .user-info .info .username {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}
.user-card .user-info .info .level {
  font-size: 28rpx;
  color: #666;
}
.user-card .login-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.user-card .login-box .avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
}
.user-card .login-box .login-btn {
  background-color: #2979ff;
  color: #fff;
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
}
.menu-list {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 0 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}
.menu-list .menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
}
.menu-list .menu-item:last-child {
  border-bottom: none;
}
.menu-list .menu-item text {
  flex: 1;
  font-size: 32rpx;
  color: #333;
  margin-left: 20rpx;
}