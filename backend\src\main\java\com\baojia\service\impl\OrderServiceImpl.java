package com.baojia.service.impl;

import com.baojia.entity.Order;
import com.baojia.entity.Product;
import com.baojia.exception.BusinessException;
import com.baojia.repository.OrderRepository;
import com.baojia.repository.ProductRepository;
import com.baojia.service.OrderService;
import com.baojia.util.Validator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class OrderServiceImpl implements OrderService {
    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private ProductRepository productRepository;

    @Override
    @Cacheable(value = "orders", key = "#keyword + '-' + #status")
    public List<Order> list(String keyword, String status) {
        if (status != null && !status.isEmpty()) {
            if (keyword != null && !keyword.trim().isEmpty()) {
                return orderRepository.searchByStatus(keyword, status);
            }
            return orderRepository.findByStatus(status);
        }
        if (keyword != null && !keyword.trim().isEmpty()) {
            return orderRepository.search(keyword);
        }
        return orderRepository.findAll();
    }

    @Override
    @Cacheable(value = "orders", key = "#id")
    public Order getById(Long id) {
        Validator.notNull(id, "订单ID不能为空");
        return orderRepository.findById(id)
                .orElseThrow(() -> new BusinessException("订单不存在"));
    }

    @Override
    @Transactional
    public Order create(Order order) {
        Validator.notNull(order, "订单信息不能为空");
        Validator.notNull(order.getProduct(), "商品信息不能为空");
        Validator.notNull(order.getQuantity(), "商品数量不能为空");

        // 检查商品库存
        Product product = productRepository.findById(order.getProduct().getId())
                .orElseThrow(() -> new BusinessException("商品不存在"));
        if (product.getStock() < order.getQuantity()) {
            throw new BusinessException("商品库存不足");
        }

        // 更新商品库存
        product.setStock(product.getStock() - order.getQuantity());
        productRepository.save(product);

        // 设置订单信息
        order.setStatus("PENDING");
        order.setCreateTime(LocalDateTime.now());
        order.setUpdateTime(LocalDateTime.now());
        order.setDeleted(0);

        return orderRepository.save(order);
    }

    @Override
    @Transactional
    public Order update(Long id, Order order) {
        Order existingOrder = getById(id);
        
        // 只允许更新部分字段
        if (order.getQuantity() != null) {
            existingOrder.setQuantity(order.getQuantity());
        }
        if (order.getRemark() != null) {
            existingOrder.setRemark(order.getRemark());
        }
        
        existingOrder.setUpdateTime(LocalDateTime.now());
        return orderRepository.save(existingOrder);
    }

    @Override
    @Transactional
    @CacheEvict(value = "orders", allEntries = true)
    public Order updateStatus(Long id, String status, String handleRemark) {
        Validator.notNull(id, "订单ID不能为空");
        Validator.notEmpty(status, "订单状态不能为空");

        Order order = getById(id);
        String oldStatus = order.getStatus();

        // 验证状态流转是否合法
        validateStatusTransition(oldStatus, status);

        // 处理库存
        handleInventory(order, oldStatus, status);

        order.setStatus(status);
        order.setHandleRemark(handleRemark);
        order.setHandleTime(LocalDateTime.now());
        order.setUpdateTime(LocalDateTime.now());
        return orderRepository.save(order);
    }

    @Override
    @Transactional
    @CacheEvict(value = "orders", allEntries = true)
    public void delete(Long id) {
        Order order = getById(id);
        order.setDeleted(1);
        order.setUpdateTime(LocalDateTime.now());
        orderRepository.save(order);
    }

    private void validateStatusTransition(String oldStatus, String newStatus) {
        switch (oldStatus) {
            case "PENDING":
                if (!newStatus.equals("PROCESSING") && !newStatus.equals("CANCELLED")) {
                    throw new BusinessException("待处理订单只能转为处理中或已取消");
                }
                break;
            case "PROCESSING":
                if (!newStatus.equals("COMPLETED") && !newStatus.equals("CANCELLED")) {
                    throw new BusinessException("处理中订单只能转为已完成或已取消");
                }
                break;
            case "COMPLETED":
                throw new BusinessException("已完成订单不能更改状态");
            case "CANCELLED":
                throw new BusinessException("已取消订单不能更改状态");
            default:
                throw new BusinessException("无效的订单状态");
        }
    }

    private void handleInventory(Order order, String oldStatus, String newStatus) {
        Product product = order.getProduct();
        
        // 如果订单被取消，恢复库存
        if (newStatus.equals("CANCELLED")) {
            product.setStock(product.getStock() + order.getQuantity());
            productRepository.save(product);
            return;
        }

        // 如果订单从取消状态恢复，减少库存
        if (oldStatus.equals("CANCELLED")) {
            if (product.getStock() < order.getQuantity()) {
                throw new BusinessException("商品库存不足");
            }
            product.setStock(product.getStock() - order.getQuantity());
            productRepository.save(product);
        }
    }
} 