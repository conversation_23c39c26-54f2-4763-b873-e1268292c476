
.container {
  padding: 20rpx;
}
.search-box {
  display: flex;
  margin-bottom: 20rpx;
}
.search-input {
  flex: 1;
  height: 72rpx;
  background: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
}
.search-btn {
  width: 120rpx;
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  background: #2979ff;
  color: #fff;
  border-radius: 36rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
}
.category-scroll {
  white-space: nowrap;
  margin-bottom: 20rpx;
}
.category-list {
  display: inline-block;
  padding: 0 20rpx;
}
.category-item {
  display: inline-block;
  padding: 16rpx 32rpx;
  margin-right: 20rpx;
  background: #f5f5f5;
  border-radius: 32rpx;
  font-size: 28rpx;
  color: #666;
}
.category-item.active {
  background: #2979ff;
  color: #fff;
}
.product-list {
  display: flex;
  flex-direction: column;
}
.product-item {
  display: flex;
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}
.product-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}
.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.product-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}
.product-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
}
.product-price {
  margin-top: auto;
}
.price {
  font-size: 36rpx;
  color: #ff6b6b;
  font-weight: bold;
}
.unit {
  font-size: 24rpx;
  color: #999;
  margin-left: 4rpx;
}
