{"version": 3, "file": "admin.js", "sources": ["api/admin.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 管理员登录\r\nexport function login(data) {\r\n  return request({\r\n    url: '/admin/login',\r\n    method: 'POST',\r\n    data\r\n  })\r\n}\r\n\r\n// 获取管理员信息\r\nexport function getInfo() {\r\n  return request({\r\n    url: '/admin/info',\r\n    method: 'GET'\r\n  })\r\n}\r\n\r\n// 获取分类列表\r\nexport function getCategories(keyword) {\r\n  return request({\r\n    url: '/admin/categories',\r\n    method: 'GET',\r\n    data: { keyword }\r\n  })\r\n}\r\n\r\n// 获取分类详情\r\nexport function getCategory(id) {\r\n  return request({\r\n    url: `/admin/categories/${id}`,\r\n    method: 'GET'\r\n  })\r\n}\r\n\r\n// 创建分类\r\nexport function createCategory(data) {\r\n  return request({\r\n    url: '/admin/categories',\r\n    method: 'POST',\r\n    data\r\n  })\r\n}\r\n\r\n// 更新分类\r\nexport function updateCategory(id, data) {\r\n  return request({\r\n    url: `/admin/categories/${id}`,\r\n    method: 'PUT',\r\n    data\r\n  })\r\n}\r\n\r\n// 删除分类\r\nexport function deleteCategory(id) {\r\n  return request({\r\n    url: `/admin/categories/${id}`,\r\n    method: 'DELETE'\r\n  })\r\n}\r\n\r\n// 获取产品列表\r\nexport function getProducts(keyword, categoryId) {\r\n  return request({\r\n    url: '/admin/products',\r\n    method: 'GET',\r\n    data: { keyword, categoryId }\r\n  })\r\n}\r\n\r\n// 获取产品详情\r\nexport function getProduct(id) {\r\n  return request({\r\n    url: `/admin/products/${id}`,\r\n    method: 'GET'\r\n  })\r\n}\r\n\r\n// 创建产品\r\nexport function createProduct(data) {\r\n  return request({\r\n    url: '/admin/products',\r\n    method: 'POST',\r\n    data\r\n  })\r\n}\r\n\r\n// 更新产品\r\nexport function updateProduct(id, data) {\r\n  return request({\r\n    url: `/admin/products/${id}`,\r\n    method: 'PUT',\r\n    data\r\n  })\r\n}\r\n\r\n// 删除产品\r\nexport function deleteProduct(id) {\r\n  return request({\r\n    url: `/admin/products/${id}`,\r\n    method: 'DELETE'\r\n  })\r\n}\r\n\r\n// 获取订单列表\r\nexport function getOrders(keyword, status) {\r\n  return request({\r\n    url: '/admin/orders',\r\n    method: 'GET',\r\n    data: { keyword, status }\r\n  })\r\n}\r\n\r\n// 获取订单详情\r\nexport function getOrder(id) {\r\n  return request({\r\n    url: `/admin/orders/${id}`,\r\n    method: 'GET'\r\n  })\r\n}\r\n\r\n// 更新订单状态\r\nexport function updateOrderStatus(id, status, handleRemark) {\r\n  return request({\r\n    url: `/admin/orders/${id}/status`,\r\n    method: 'PUT',\r\n    data: { status, handleRemark }\r\n  })\r\n}\r\n\r\n// 上传文件\r\nexport function uploadFile(file) {\r\n  return request({\r\n    url: '/admin/files/upload',\r\n    method: 'POST',\r\n    header: {\r\n      'Content-Type': 'multipart/form-data'\r\n    },\r\n    data: file\r\n  })\r\n}\r\n\r\n// 删除文件\r\nexport function deleteFile(filename) {\r\n  return request({\r\n    url: `/admin/files/${filename}`,\r\n    method: 'DELETE'\r\n  })\r\n} "], "names": ["request"], "mappings": ";;AAGO,SAAS,MAAM,MAAM;AAC1B,SAAOA,sBAAQ;AAAA,IACb,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACJ,CAAG;AACH;AAWO,SAAS,cAAc,SAAS;AACrC,SAAOA,sBAAQ;AAAA,IACb,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,MAAM,EAAE,QAAS;AAAA,EACrB,CAAG;AACH;AA6BO,SAAS,eAAe,IAAI;AACjC,SAAOA,sBAAQ;AAAA,IACb,KAAK,qBAAqB,EAAE;AAAA,IAC5B,QAAQ;AAAA,EACZ,CAAG;AACH;AAGO,SAAS,YAAY,SAAS,YAAY;AAC/C,SAAOA,sBAAQ;AAAA,IACb,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,MAAM,EAAE,SAAS,WAAY;AAAA,EACjC,CAAG;AACH;AA6BO,SAAS,cAAc,IAAI;AAChC,SAAOA,sBAAQ;AAAA,IACb,KAAK,mBAAmB,EAAE;AAAA,IAC1B,QAAQ;AAAA,EACZ,CAAG;AACH;AAGO,SAAS,UAAU,SAAS,QAAQ;AACzC,SAAOA,sBAAQ;AAAA,IACb,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,MAAM,EAAE,SAAS,OAAQ;AAAA,EAC7B,CAAG;AACH;AAWO,SAAS,kBAAkB,IAAI,QAAQ,cAAc;AAC1D,SAAOA,sBAAQ;AAAA,IACb,KAAK,iBAAiB,EAAE;AAAA,IACxB,QAAQ;AAAA,IACR,MAAM,EAAE,QAAQ,aAAc;AAAA,EAClC,CAAG;AACH;;;;;;;;"}