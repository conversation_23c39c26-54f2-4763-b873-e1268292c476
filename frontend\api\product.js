import request from '@/utils/request'

// 获取产品列表
export function getProducts(params) {
  return request({
    url: '/api/products',
    method: 'GET',
    params
  })
}

// 获取产品详情
export function getProduct(id) {
  return request({
    url: `/api/products/${id}`,
    method: 'GET'
  })
}

// 获取分类下的产品
export function getCategoryProducts(categoryId) {
  return request({
    url: '/api/products',
    method: 'GET',
    params: {
      categoryId
    }
  })
}

// 创建产品
export function createProduct(data) {
  return request({
    url: '/api/products',
    method: 'POST',
    data
  })
}

// 更新产品
export function updateProduct(id, data) {
  return request({
    url: `/api/products/${id}`,
    method: 'PUT',
    data
  })
}

// 删除产品
export function deleteProduct(id) {
  return request({
    url: `/api/products/${id}`,
    method: 'DELETE'
  })
}

// 更新产品状态
export function updateProductStatus(id, enabled) {
  return request({
    url: `/product/${id}/status`,
    method: 'PUT',
    data: { enabled }
  })
}

// 上传产品图片
export function uploadProductImage(file) {
  return request({
    url: '/file/upload',
    method: 'POST',
    header: {
      'Content-Type': 'multipart/form-data'
    },
    data: file
  })
} 