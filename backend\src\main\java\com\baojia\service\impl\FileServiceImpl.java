package com.baojia.service.impl;

import com.baojia.service.FileService;
import com.baojia.exception.BusinessException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.UUID;

@Service
public class FileServiceImpl implements FileService {
    @Value("${file.upload.path}")
    private String uploadPath;

    @Value("${file.upload.url}")
    private String uploadUrl;

    @Override
    public String upload(MultipartFile file) {
        // 检查文件是否为空
        if (file.isEmpty()) {
            throw new BusinessException("文件不能为空");
        }
        
        // 检查文件格式
        String originalFilename = file.getOriginalFilename();
        String extension = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase();
        if (!extension.matches("png|jpg|jpeg")) {
            throw new BusinessException("仅支持png、jpg格式图片");
        }
        
        try {
            // 读取图片
            BufferedImage originalImage = ImageIO.read(file.getInputStream());
            if (originalImage == null) {
                throw new BusinessException("无效的图片文件");
            }
            
            // 生成新的文件名（统一使用png格式）
            String newFilename = UUID.randomUUID().toString() + ".png";
            
            // 确保上传目录存在
            File uploadDir = new File(uploadPath);
            if (!uploadDir.exists()) {
                uploadDir.mkdirs();
            }
            
            // 保存为PNG格式
            File destFile = new File(uploadPath + File.separator + newFilename);
            ImageIO.write(originalImage, "png", destFile);
            
            return uploadUrl + "/" + newFilename;
        } catch (IOException e) {
            throw new BusinessException("文件上传失败");
        }
    }

    @Override
    public void delete(String filename) {
        File file = new File(uploadPath + File.separator + filename);
        if (file.exists() && file.isFile()) {
            file.delete();
        }
    }
} 