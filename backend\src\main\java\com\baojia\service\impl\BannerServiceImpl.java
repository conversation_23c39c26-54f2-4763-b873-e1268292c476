package com.baojia.service.impl;

import com.baojia.entity.Banner;
import com.baojia.exception.BusinessException;
import com.baojia.repository.BannerRepository;
import com.baojia.service.BannerService;
import com.baojia.util.Validator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class BannerServiceImpl implements BannerService {
    @Autowired
    private BannerRepository bannerRepository;

    @Override
    @Cacheable(value = "banners", key = "'all'")
    public List<Banner> list() {
        return bannerRepository.findAllActive();
    }

    @Override
    @Cacheable(value = "banners", key = "#id")
    public Banner getById(Long id) {
        Validator.notNull(id, "轮播图ID不能为空");
        return bannerRepository.findById(id)
                .orElseThrow(() -> new BusinessException("轮播图不存在"));
    }

    @Override
    @Transactional
    @CacheEvict(value = "banners", allEntries = true)
    public Banner create(Banner banner) {
        Validator.notNull(banner, "轮播图信息不能为空");
        Validator.notEmpty(banner.getImage(), "轮播图图片不能为空");
        return bannerRepository.save(banner);
    }

    @Override
    @Transactional
    @CacheEvict(value = "banners", allEntries = true)
    public Banner update(Long id, Banner banner) {
        Validator.notNull(id, "轮播图ID不能为空");
        Validator.notNull(banner, "轮播图信息不能为空");
        Validator.notEmpty(banner.getImage(), "轮播图图片不能为空");

        Banner existingBanner = getById(id);
        existingBanner.setImage(banner.getImage());
        existingBanner.setLink(banner.getLink());
        existingBanner.setSort(banner.getSort());
        existingBanner.setStatus(banner.getStatus());
        return bannerRepository.save(existingBanner);
    }

    @Override
    @Transactional
    @CacheEvict(value = "banners", allEntries = true)
    public void delete(Long id) {
        Validator.notNull(id, "轮播图ID不能为空");
        bannerRepository.deleteById(id);
    }
} 