package com.baojia.service.impl;

import com.baojia.entity.User;
import com.baojia.exception.AuthException;
import com.baojia.exception.BusinessException;
import com.baojia.model.LoginRequest;
import com.baojia.repository.UserRepository;
import com.baojia.service.UserService;
import com.baojia.util.JwtUtil;
import com.baojia.util.Validator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.security.core.context.SecurityContextHolder;
import java.time.LocalDateTime;

@Service
public class UserServiceImpl implements UserService {
    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private JwtUtil jwtUtil;

    @Override
    public String login(LoginRequest request) {
        logger.debug("开始处理登录请求: {}", request.getPhone());
        
        Validator.notEmpty(request.getPhone(), "手机号不能为空");
        Validator.notEmpty(request.getPassword(), "密码不能为空");
        
        User user = userRepository.findByPhone(request.getPhone())
                .orElseThrow(() -> {
                    logger.warn("用户不存在: {}", request.getPhone());
                    return new BusinessException("用户不存在");
                });
        
        logger.debug("找到用户: {}", user.getPhone());
        
        if (!request.getPassword().equals(user.getPassword())) {
            logger.warn("密码错误: {}", request.getPhone());
            throw new BusinessException("密码错误");
        }
        
        logger.debug("密码验证通过");
        
        // 更新最后登录时间
        user.setLastLoginTime(LocalDateTime.now());
        userRepository.save(user);
        
        String token = jwtUtil.generateToken(user.getPhone());
        logger.debug("生成token成功");
        
        return token;
    }

    @Override
    @Transactional
    public User register(User user) {
        Validator.notEmpty(user.getPhone(), "手机号不能为空");
        Validator.notEmpty(user.getPassword(), "密码不能为空");
        
        if (userRepository.existsByPhone(user.getPhone())) {
            throw new BusinessException("手机号已注册");
        }
        
        user.setUserType(0);
        user.setMemberLevel("普通会员");
        user.setDeleted(0);
        
        return userRepository.save(user);
    }

    @Override
    public void sendCode(String phone) {
        Validator.notEmpty(phone, "手机号不能为空");
        
        // TODO: 实现发送验证码逻辑
        // 1. 生成验证码
        // 2. 保存验证码（可以使用Redis）
        // 3. 发送验证码（可以使用短信服务）
    }

    @Override
    public User getCurrentUser() {
        Object credentials = SecurityContextHolder.getContext().getAuthentication().getCredentials();
        if (credentials == null) {
            throw new AuthException("未登录或token已过期");
        }
        String token = credentials.toString();
        String phone = jwtUtil.getPhoneFromToken(token);
        return userRepository.findByPhone(phone)
                .orElseThrow(() -> new AuthException("用户不存在"));
    }

    @Override
    @Transactional
    public User updateCurrentUser(User user) {
        User currentUser = getCurrentUser();
        
        // 只允许更新部分字段
        if (user.getRealName() != null) {
            currentUser.setRealName(user.getRealName());
        }
        if (user.getEmail() != null) {
            currentUser.setEmail(user.getEmail());
        }
        if (user.getAddress() != null) {
            currentUser.setAddress(user.getAddress());
        }
        
        return userRepository.save(currentUser);
    }
} 