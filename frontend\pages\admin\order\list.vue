<template>
  <view class="container">
    <view class="header">
      <view class="search-box">
        <input 
          type="text" 
          v-model="searchKeyword" 
          placeholder="搜索订单号/用户名" 
          class="search-input"
          @confirm="handleSearch"
        />
        <button class="search-btn" @tap="handleSearch">搜索</button>
      </view>
      <view class="filter-box">
        <picker 
          mode="selector" 
          :range="statusOptions" 
          range-key="label"
          :value="statusIndex"
          @change="handleStatusChange"
          class="status-picker"
        >
          <view class="picker-text">
            {{ statusOptions[statusIndex].label }}
          </view>
        </picker>
      </view>
    </view>

    <view class="order-list">
      <view class="order-item" v-for="(item, index) in orders" :key="index" @tap="goToDetail(item.id)">
        <view class="order-header">
          <text class="order-no">订单号：{{ item.orderNo }}</text>
          <text class="order-status" :class="item.status">{{ getStatusText(item.status) }}</text>
        </view>
        <view class="order-content">
          <view class="product-info">
            <image :src="item.productImage" mode="aspectFill" class="product-image"/>
            <view class="product-detail">
              <text class="product-name">{{ item.productName }}</text>
              <text class="product-spec">{{ item.productSpec }}</text>
              <text class="product-price">¥{{ item.price }}</text>
            </view>
          </view>
          <view class="order-info">
            <text class="info-item">下单时间：{{ item.createTime }}</text>
            <text class="info-item">用户：{{ item.username }}</text>
            <text class="info-item">联系电话：{{ item.phone }}</text>
          </view>
        </view>
        <view class="order-footer">
          <text class="total-price">总计：¥{{ item.totalPrice }}</text>
          <view class="action-btns">
            <button 
              class="action-btn" 
              v-if="item.status === 'pending'" 
              @tap.stop="handleUpdateStatus(item.id, 'processing')"
            >接单</button>
            <button 
              class="action-btn" 
              v-if="item.status === 'processing'" 
              @tap.stop="handleUpdateStatus(item.id, 'completed')"
            >完成</button>
            <button 
              class="action-btn cancel" 
              v-if="item.status === 'pending'" 
              @tap.stop="handleUpdateStatus(item.id, 'cancelled')"
            >取消</button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getOrders, updateOrderStatus } from '@/api/admin'

export default {
  data() {
    return {
      searchKeyword: '',
      statusOptions: [
        { value: '', label: '全部状态' },
        { value: 'pending', label: '待处理' },
        { value: 'processing', label: '处理中' },
        { value: 'completed', label: '已完成' },
        { value: 'cancelled', label: '已取消' }
      ],
      statusIndex: 0,
      orders: []
    }
  },
  onLoad() {
    this.loadOrders()
  },
  methods: {
    loadOrders() {
      getOrders({
        keyword: this.searchKeyword,
        status: this.statusOptions[this.statusIndex].value
      }).then(res => {
        this.orders = res.data
      })
    },
    handleSearch() {
      this.loadOrders()
    },
    handleStatusChange(e) {
      this.statusIndex = e.detail.value
      this.loadOrders()
    },
    getStatusText(status) {
      const statusMap = {
        pending: '待处理',
        processing: '处理中',
        completed: '已完成',
        cancelled: '已取消'
      }
      return statusMap[status] || status
    },
    goToDetail(id) {
      uni.navigateTo({
        url: `/pages/admin/order/detail?id=${id}`
      })
    },
    handleUpdateStatus(id, status) {
      uni.showModal({
        title: '提示',
        content: `确定要${this.getStatusText(status)}该订单吗？`,
        success: (res) => {
          if (res.confirm) {
            updateOrderStatus(id, { status }).then(() => {
              uni.showToast({
                title: '更新成功'
              })
              this.loadOrders()
            })
          }
        }
      })
    }
  }
}
</script>

<style>
.container {
  padding: 20rpx;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.search-box {
  display: flex;
  flex: 1;
  margin-right: 20rpx;
}

.search-input {
  flex: 1;
  height: 72rpx;
  background: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
}

.search-btn {
  width: 120rpx;
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  background: #2979ff;
  color: #fff;
  border-radius: 36rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
}

.filter-box {
  width: 200rpx;
}

.status-picker {
  width: 100%;
  height: 72rpx;
  background: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 30rpx;
}

.picker-text {
  line-height: 72rpx;
  font-size: 28rpx;
  color: #333;
}

.order-list {
  display: flex;
  flex-direction: column;
}

.order-item {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.order-no {
  font-size: 28rpx;
  color: #666;
}

.order-status {
  font-size: 28rpx;
  padding: 4rpx 16rpx;
  border-radius: 24rpx;
}

.pending {
  color: #ff9900;
  background: #fff9e6;
}

.processing {
  color: #2979ff;
  background: #e6f3ff;
}

.completed {
  color: #19be6b;
  background: #e6fff0;
}

.cancelled {
  color: #999;
  background: #f5f5f5;
}

.order-content {
  border-top: 1rpx solid #eee;
  border-bottom: 1rpx solid #eee;
  padding: 20rpx 0;
}

.product-info {
  display: flex;
  margin-bottom: 20rpx;
}

.product-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.product-detail {
  flex: 1;
}

.product-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.product-spec {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.product-price {
  font-size: 32rpx;
  color: #ff6b6b;
}

.order-info {
  display: flex;
  flex-direction: column;
}

.info-item {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
}

.total-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff6b6b;
}

.action-btns {
  display: flex;
}

.action-btn {
  width: 120rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  background: #2979ff;
  color: #fff;
  border-radius: 30rpx;
  font-size: 24rpx;
  margin-left: 20rpx;
}

.cancel {
  background: #ff6b6b;
}
</style> 