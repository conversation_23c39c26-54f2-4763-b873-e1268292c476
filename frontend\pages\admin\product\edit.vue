<template>
  <view class="product-edit">
    <view class="form">
      <view class="form-item">
        <text class="label">产品名称</text>
        <input type="text" v-model="form.name" placeholder="请输入产品名称" />
      </view>
      
      <view class="form-item">
        <text class="label">产品分类</text>
        <picker mode="selector" :range="categories" range-key="name" @change="handleCategoryChange">
          <view class="picker">
            {{ form.categoryId ? categories.find(c => c.id === form.categoryId)?.name : '请选择分类' }}
          </view>
        </picker>
      </view>
      
      <view class="form-item">
        <text class="label">产品价格</text>
        <input type="digit" v-model="form.price" placeholder="请输入产品价格" />
      </view>
      
      <view class="form-item">
        <text class="label">产品库存</text>
        <input type="number" v-model="form.stock" placeholder="请输入产品库存" />
      </view>

      <view class="form-item">
        <text class="label">分辨率</text>
        <input type="text" v-model="form.resolution" placeholder="请输入分辨率，如：1080P" />
      </view>

      <view class="form-item">
        <text class="label">夜视功能</text>
        <input type="text" v-model="form.nightVision" placeholder="请输入夜视功能，如：30米红外" />
      </view>

      <view class="form-item">
        <text class="label">存储容量</text>
        <input type="text" v-model="form.storage" placeholder="请输入存储容量，如：支持最大128G" />
      </view>

      <view class="form-item">
        <text class="label">连接方式</text>
        <input type="text" v-model="form.connection" placeholder="请输入连接方式，如：POE/网线" />
      </view>
      
      <view class="form-item">
        <text class="label">产品图片</text>
        <view class="upload-box" @click="handleUpload">
          <image v-if="form.mainImage" :src="form.mainImage" mode="aspectFill" class="preview" />
          <view v-else class="upload-btn">
            <text class="icon">+</text>
            <text>上传图片</text>
          </view>
        </view>
      </view>
      
      <view class="form-item">
        <text class="label">产品描述</text>
        <textarea v-model="form.description" placeholder="请输入产品描述" />
      </view>

      <view class="form-item">
        <text class="label">排序号</text>
        <input type="number" v-model="form.sort" placeholder="请输入排序号" />
      </view>

      <view class="form-item">
        <text class="label">状态</text>
        <switch :checked="form.status === 1" @change="e => form.status = e.detail.value ? 1 : 0" />
      </view>

      <view class="form-item">
        <text class="label">热门</text>
        <switch :checked="form.isHot === 1" @change="e => form.isHot = e.detail.value ? 1 : 0" />
      </view>

      <view class="form-item">
        <text class="label">新品</text>
        <switch :checked="form.isNew === 1" @change="e => form.isNew = e.detail.value ? 1 : 0" />
      </view>

      <view class="form-item">
        <text class="label">推荐</text>
        <switch :checked="form.isRecommend === 1" @change="e => form.isRecommend = e.detail.value ? 1 : 0" />
      </view>
    </view>
    
    <view class="actions">
      <button class="cancel-btn" @click="handleCancel">取消</button>
      <button class="submit-btn" @click="handleSubmit">保存</button>
    </view>
  </view>
</template>

<script>
import { getProductDetail, createProduct, updateProduct, uploadFile } from '@/api/admin'
import { getCategoryList } from '@/api/admin'

export default {
  data() {
    return {
      id: null,
      form: {
        name: '',
        categoryId: null,
        price: '',
        stock: '',
        mainImage: '',
        description: '',
        resolution: '',
        nightVision: '',
        storage: '',
        connection: '',
        sort: 0,
        status: 1,
        isHot: 0,
        isNew: 0,
        isRecommend: 0
      },
      categories: []
    }
  },
  onLoad(options) {
    if (options.id) {
      this.id = options.id
      this.getDetail()
    }
    this.getCategories()
  },
  methods: {
    async getDetail() {
      try {
        const data = await getProductDetail(this.id)
        this.form = {
          ...data,
          categoryId: data.category.id
        }
      } catch (error) {
        console.error('获取产品详情失败:', error)
        uni.showToast({
          title: '获取产品详情失败',
          icon: 'none'
        })
      }
    },
    async getCategories() {
      try {
        const res = await getCategoryList()
        this.categories = res.data
      } catch (error) {
        console.error('获取分类列表失败:', error)
        uni.showToast({
          title: '获取分类列表失败',
          icon: 'none'
        })
      }
    },
    handleCategoryChange(e) {
      const index = e.detail.value
      this.form.categoryId = this.categories[index].id
    },
    async handleUpload() {
      try {
        const [tempFile] = await uni.chooseImage({
          count: 1,
          sizeType: ['compressed'],
          sourceType: ['album', 'camera']
        })
        
        const res = await uploadFile(tempFile.tempFilePaths[0])
        this.form.mainImage = res.url
      } catch (error) {
        console.error('上传图片失败:', error)
        uni.showToast({
          title: '上传图片失败',
          icon: 'none'
        })
      }
    },
    handleCancel() {
      uni.navigateBack()
    },
    async handleSubmit() {
      try {
        if (!this.form.name) {
          throw new Error('请输入产品名称')
        }
        if (!this.form.categoryId) {
          throw new Error('请选择产品分类')
        }
        if (!this.form.price) {
          throw new Error('请输入产品价格')
        }
        if (!this.form.stock) {
          throw new Error('请输入产品库存')
        }
        if (!this.form.mainImage) {
          throw new Error('请上传产品图片')
        }

        const submitData = {
          ...this.form,
          category: { id: this.form.categoryId }
        }

        if (this.id) {
          await updateProduct(this.id, submitData)
        } else {
          await createProduct(submitData)
        }

        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })
        
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      } catch (error) {
        console.error('保存失败:', error)
        uni.showToast({
          title: error.message || '保存失败',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style lang="scss">
.product-edit {
  padding: 20rpx;
  
  .form {
    background: #fff;
    border-radius: 12rpx;
    padding: 20rpx;
    
    .form-item {
      margin-bottom: 30rpx;
      
      .label {
        display: block;
        margin-bottom: 10rpx;
        font-size: 28rpx;
        color: #333;
      }
      
      input, textarea {
        width: 100%;
        border: 1px solid #ddd;
        border-radius: 6rpx;
        padding: 16rpx;
        font-size: 28rpx;
        box-sizing: border-box;
      }
      
      textarea {
        height: 200rpx;
      }
      
      .picker {
        border: 1px solid #ddd;
        border-radius: 6rpx;
        padding: 16rpx;
        font-size: 28rpx;
      }
      
      .upload-box {
        width: 200rpx;
        height: 200rpx;
        border: 1px dashed #ddd;
        border-radius: 6rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .preview {
          width: 100%;
          height: 100%;
          border-radius: 6rpx;
        }
        
        .upload-btn {
          text-align: center;
          color: #999;
          
          .icon {
            font-size: 48rpx;
            display: block;
          }
        }
      }
    }
  }
  
  .actions {
    margin-top: 40rpx;
    display: flex;
    justify-content: space-between;
    
    button {
      width: 45%;
      
      &.cancel-btn {
        background: #f5f5f5;
        color: #666;
      }
      
      &.submit-btn {
        background: #007aff;
        color: #fff;
      }
    }
  }
}
</style> 