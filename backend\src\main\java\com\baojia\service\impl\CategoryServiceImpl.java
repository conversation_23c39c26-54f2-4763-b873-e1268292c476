package com.baojia.service.impl;

import com.baojia.entity.Category;
import com.baojia.exception.BusinessException;
import com.baojia.repository.CategoryRepository;
import com.baojia.service.CategoryService;
import com.baojia.util.Validator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class CategoryServiceImpl implements CategoryService {
    private static final Logger logger = LoggerFactory.getLogger(CategoryServiceImpl.class);

    @Autowired
    private CategoryRepository categoryRepository;

    @Override
    @Cacheable(value = "categories", key = "#keyword == null ? 'all' : #keyword")
    public List<Category> list(String keyword) {
        logger.info("获取分类列表 - keyword: {}", keyword);
        List<Category> categories;
        if (keyword == null || keyword.trim().isEmpty()) {
            categories = categoryRepository.findAllActive();
            logger.info("获取所有激活分类 - 数量: {}", categories.size());
            return categories;
        }
        categories = categoryRepository.search(keyword);
        logger.info("搜索分类 - keyword: {}, 数量: {}", keyword, categories.size());
        return categories;
    }

    @Override
    @Cacheable(value = "categories", key = "#id")
    public Category getById(Long id) {
        Validator.notNull(id, "分类ID不能为空");
        return categoryRepository.findById(id)
                .orElseThrow(() -> new BusinessException("分类不存在"));
    }

    @Override
    @Transactional
    @CacheEvict(value = "categories", allEntries = true)
    public Category create(Category category) {
        Validator.notNull(category, "分类信息不能为空");
        Validator.notEmpty(category.getName(), "分类名称不能为空");
        
        // 检查分类名称是否已存在
        if (categoryRepository.existsByName(category.getName())) {
            throw new BusinessException("分类名称已存在");
        }
        
        return categoryRepository.save(category);
    }

    @Override
    @Transactional
    @CacheEvict(value = "categories", allEntries = true)
    public Category update(Long id, Category category) {
        Validator.notNull(id, "分类ID不能为空");
        Validator.notNull(category, "分类信息不能为空");
        Validator.notEmpty(category.getName(), "分类名称不能为空");

        Category existingCategory = getById(id);
        
        // 如果修改了名称，检查新名称是否已存在
        if (!existingCategory.getName().equals(category.getName()) 
            && categoryRepository.existsByName(category.getName())) {
            throw new BusinessException("分类名称已存在");
        }

        existingCategory.setName(category.getName());
        existingCategory.setDescription(category.getDescription());
        existingCategory.setIcon(category.getIcon());
        return categoryRepository.save(existingCategory);
    }

    @Override
    @Transactional
    @CacheEvict(value = "categories", allEntries = true)
    public void delete(Long id) {
        Validator.notNull(id, "分类ID不能为空");
        
        // 检查分类是否有关联的产品
        if (categoryRepository.hasProducts(id)) {
            throw new BusinessException("该分类下存在产品，无法删除");
        }
        
        categoryRepository.deleteById(id);
    }
} 