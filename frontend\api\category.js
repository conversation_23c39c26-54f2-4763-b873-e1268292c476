import request from '@/utils/request'

// 获取分类列表
export function getCategoryList() {
  return request({
    url: '/api/categories',
    method: 'GET'
  })
}

// 获取分类详情
export function getCategory(id) {
  return request({
    url: `/api/categories/${id}`,
    method: 'GET'
  })
}

// 创建分类
export function createCategory(data) {
  return request({
    url: '/api/categories',
    method: 'POST',
    data
  })
}

// 更新分类
export function updateCategory(id, data) {
  return request({
    url: `/api/categories/${id}`,
    method: 'PUT',
    data
  })
}

// 删除分类
export function deleteCategory(id) {
  return request({
    url: `/api/categories/${id}`,
    method: 'DELETE'
  })
}

// 更新分类状态
export function updateCategoryStatus(id, enabled) {
  return request({
    url: `/category/${id}/status`,
    method: 'PUT',
    data: { enabled }
  })
} 