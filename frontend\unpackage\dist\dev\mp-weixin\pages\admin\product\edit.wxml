<view class="edit-container"><view class="form-box"><view class="form-item"><text class="label">产品名称</text><input type="text" placeholder="请输入产品名称" maxlength="100" value="{{a}}" bindinput="{{b}}"/></view><view class="form-item"><text class="label">所属分类</text><picker mode="selector" range="{{d}}" range-key="name" value="{{e}}" bindchange="{{f}}"><view class="picker">{{c}}</view></picker></view><view class="form-item"><text class="label">产品价格</text><input type="digit" placeholder="请输入产品价格" value="{{g}}" bindinput="{{h}}"/></view><view class="form-item"><text class="label">库存数量</text><input type="number" placeholder="请输入库存数量" value="{{i}}" bindinput="{{j}}"/></view><view class="form-item"><text class="label">产品描述</text><block wx:if="{{r0}}"><textarea placeholder="请输入产品描述" maxlength="500" value="{{k}}" bindinput="{{l}}"/></block></view><view class="form-item"><text class="label">产品图片</text><view class="image-list"><view wx:for="{{m}}" wx:for-item="image" wx:key="c" class="image-item"><image src="{{image.a}}" mode="aspectFill"/><text class="delete-btn" bindtap="{{image.b}}">×</text></view><view wx:if="{{n}}" class="upload-btn" bindtap="{{o}}"><text class="icon">+</text></view></view></view><view class="form-item"><text class="label">状态</text><switch checked="{{p}}" bindchange="{{q}}" color="#007AFF"/></view><view class="btn-group"><button class="btn save" bindtap="{{r}}">保存</button><button class="btn cancel" bindtap="{{s}}">取消</button></view></view></view>