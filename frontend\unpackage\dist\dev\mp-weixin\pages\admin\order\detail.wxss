/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}
.detail-container .order-info {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.detail-container .order-info .info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.detail-container .order-info .info-item:last-child {
  margin-bottom: 0;
}
.detail-container .order-info .info-item .label {
  color: #666;
  font-size: 28rpx;
}
.detail-container .order-info .info-item .value {
  color: #333;
  font-size: 28rpx;
}
.detail-container .order-info .info-item .value.price {
  color: #f00;
  font-weight: bold;
}
.detail-container .product-list {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.detail-container .product-list .title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.detail-container .product-list .product-item {
  display: flex;
  margin-bottom: 20rpx;
}
.detail-container .product-list .product-item:last-child {
  margin-bottom: 0;
}
.detail-container .product-list .product-item image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 6rpx;
  margin-right: 20rpx;
}
.detail-container .product-list .product-item .info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.detail-container .product-list .product-item .info .name {
  font-size: 28rpx;
  color: #333;
}
.detail-container .product-list .product-item .info .price {
  font-size: 28rpx;
  color: #f00;
}
.detail-container .product-list .product-item .info .quantity {
  font-size: 24rpx;
  color: #999;
}
.detail-container .handle-box {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 30rpx;
}
.detail-container .handle-box .title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.detail-container .handle-box .form-item {
  margin-bottom: 30rpx;
}
.detail-container .handle-box .form-item .label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.detail-container .handle-box .form-item textarea {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #ddd;
  border-radius: 6rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.detail-container .handle-box .btn-group {
  display: flex;
  justify-content: space-between;
}
.detail-container .handle-box .btn-group .btn {
  width: 45%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 6rpx;
  font-size: 32rpx;
}
.detail-container .handle-box .btn-group .btn.accept {
  background-color: #007AFF;
  color: #fff;
}
.detail-container .handle-box .btn-group .btn.reject {
  background-color: #f5f5f5;
  color: #666;
}