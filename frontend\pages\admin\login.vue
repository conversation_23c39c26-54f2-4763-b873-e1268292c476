<template>
  <view class="login-container">
    <view class="login-box">
      <view class="title">管理员登录</view>
      
      <!-- 登录表单 -->
      <view class="form">
        <view class="form-item">
          <input 
            type="text" 
            v-model="form.phone" 
            placeholder="请输入手机号"
            maxlength="11"
          />
        </view>
        <view class="form-item">
          <input 
            type="password" 
            v-model="form.password" 
            placeholder="请输入密码"
          />
        </view>
        <button class="login-btn" @click="handleLogin">登录</button>
      </view>
      
      <!-- 微信登录 -->
      <view class="wechat-login">
        <view class="divider">
          <text>或</text>
        </view>
        <button class="wechat-btn" @click="handleWechatLogin">
          <text class="iconfont icon-wechat"></text>
          微信登录
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { login } from '@/api/admin'

export default {
  data() {
    return {
      form: {
        phone: '',
        password: ''
      }
    }
  },
  methods: {
    // 手机号密码登录
    async handleLogin() {
      if (!this.form.phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return
      }
      if (!this.form.password) {
        uni.showToast({
          title: '请输入密码',
          icon: 'none'
        })
        return
      }
      
      try {
        const res = await login(this.form)
        uni.setStorageSync('token', res.token)
        uni.reLaunch({
          url: '/pages/admin/index'
        })
      } catch (error) {
        console.error('登录失败:', error)
      }
    },
    
    // 微信登录
    handleWechatLogin() {
      // #ifdef APP-PLUS
      uni.login({
        provider: 'weixin',
        onlyAuthorize: true,
        success: async (loginRes) => {
          try {
            // 获取用户信息
            const userInfo = await this.getWechatUserInfo()
            // 发送登录请求
            const res = await login({
              code: loginRes.code,
              userInfo
            })
            uni.setStorageSync('token', res.token)
            uni.reLaunch({
              url: '/pages/admin/index'
            })
          } catch (error) {
            console.error('微信登录失败:', error)
          }
        },
        fail: (err) => {
          console.error('微信登录失败:', err)
          uni.showToast({
            title: '微信登录失败',
            icon: 'none'
          })
        }
      })
      // #endif
      
      // #ifdef H5
      uni.showToast({
        title: 'H5端暂不支持微信登录',
        icon: 'none'
      })
      // #endif
    },
    
    // 获取微信用户信息
    getWechatUserInfo() {
      return new Promise((resolve, reject) => {
        uni.getUserInfo({
          provider: 'weixin',
          success: (res) => {
            resolve(res.userInfo)
          },
          fail: (err) => {
            reject(err)
          }
        })
      })
    }
  }
}
</script>

<style lang="scss">
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  
  .login-box {
    width: 600rpx;
    padding: 40rpx;
    background-color: #fff;
    border-radius: 20rpx;
    box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.1);
    
    .title {
      font-size: 36rpx;
      font-weight: bold;
      text-align: center;
      margin-bottom: 40rpx;
    }
    
    .form {
      .form-item {
        margin-bottom: 20rpx;
        
        input {
          width: 100%;
          height: 80rpx;
          padding: 0 20rpx;
          border: 2rpx solid #ddd;
          border-radius: 10rpx;
          font-size: 28rpx;
        }
      }
      
      .login-btn {
        width: 100%;
        height: 80rpx;
        line-height: 80rpx;
        background-color: #007AFF;
        color: #fff;
        font-size: 32rpx;
        border-radius: 10rpx;
        margin-top: 40rpx;
      }
    }
    
    .wechat-login {
      margin-top: 40rpx;
      
      .divider {
        position: relative;
        text-align: center;
        margin: 20rpx 0;
        
        &::before,
        &::after {
          content: '';
          position: absolute;
          top: 50%;
          width: 30%;
          height: 2rpx;
          background-color: #ddd;
        }
        
        &::before {
          left: 0;
        }
        
        &::after {
          right: 0;
        }
        
        text {
          display: inline-block;
          padding: 0 20rpx;
          color: #999;
          font-size: 24rpx;
          background-color: #fff;
        }
      }
      
      .wechat-btn {
        width: 100%;
        height: 80rpx;
        line-height: 80rpx;
        background-color: #07C160;
        color: #fff;
        font-size: 32rpx;
        border-radius: 10rpx;
        
        .iconfont {
          margin-right: 10rpx;
        }
      }
    }
  }
}
</style> 