import request from '../utils/request'

// 获取订单列表
export function getOrderList(params) {
  return request({
    url: '/order',
    method: 'GET',
    data: params
  })
}

// 获取订单详情
export function getOrderDetail(id) {
  return request({
    url: `/order/${id}`,
    method: 'GET'
  })
}

// 更新订单状态
export function updateOrderStatus(id, status, handleRemark) {
  return request({
    url: `/order/${id}/status`,
    method: 'PUT',
    data: { status, handleRemark }
  })
}

// 删除订单
export function deleteOrder(id) {
  return request({
    url: `/order/${id}`,
    method: 'DELETE'
  })
} 