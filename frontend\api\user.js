import request from '@/utils/request'

// 用户登录
export function login(data) {
  return request({
    url: '/api/user/login',
    method: 'post',
    data: {
      phone: data.phone,
      password: data.password
    }
  })
}

// 用户注册
export function register(data) {
  return request({
    url: '/api/user/register',
    method: 'post',
    data
  })
}

// 发送验证码
export function sendCode(phone) {
  return request({
    url: '/api/user/sendCode',
    method: 'post',
    data: { phone }
  })
}

// 获取当前用户信息
export function getCurrentUser() {
  return request({
    url: '/api/user/current',
    method: 'get'
  })
}

// 更新当前用户信息
export function updateCurrentUser(data) {
  return request({
    url: '/api/user/current',
    method: 'put',
    data
  })
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: '/api/user/info',
    method: 'get'
  })
} 