"use strict";
const common_vendor = require("../../../common/vendor.js");
const api_category = require("../../../api/category.js");
const _sfc_main = {
  data() {
    return {
      id: null,
      form: {
        name: "",
        description: "",
        sort: 0,
        enabled: true
      }
    };
  },
  onLoad(options) {
    if (options.id) {
      this.id = options.id;
      this.loadCategory();
    }
  },
  methods: {
    async loadCategory() {
      try {
        const res = await api_category.getCategory(this.id);
        this.form = res.data;
      } catch (error) {
        common_vendor.index.showToast({
          title: "加载分类信息失败",
          icon: "none"
        });
      }
    },
    handleStatusChange(e) {
      this.form.enabled = e.detail.value;
    },
    async handleSave() {
      if (!this.form.name) {
        common_vendor.index.showToast({
          title: "请输入分类名称",
          icon: "none"
        });
        return;
      }
      try {
        if (this.id) {
          await api_category.updateCategory(this.id, this.form);
        } else {
          await api_category.createCategory(this.form);
        }
        common_vendor.index.showToast({
          title: "保存成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      } catch (error) {
        common_vendor.index.showToast({
          title: error.message || "保存失败",
          icon: "none"
        });
      }
    },
    handleCancel() {
      common_vendor.index.navigateBack();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $data.form.name,
    b: common_vendor.o(($event) => $data.form.name = $event.detail.value),
    c: $data.form.description,
    d: common_vendor.o(($event) => $data.form.description = $event.detail.value),
    e: $data.form.sort,
    f: common_vendor.o(($event) => $data.form.sort = $event.detail.value),
    g: $data.form.enabled,
    h: common_vendor.o((...args) => $options.handleStatusChange && $options.handleStatusChange(...args)),
    i: common_vendor.o((...args) => $options.handleSave && $options.handleSave(...args)),
    j: common_vendor.o((...args) => $options.handleCancel && $options.handleCancel(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/admin/category/edit.js.map
