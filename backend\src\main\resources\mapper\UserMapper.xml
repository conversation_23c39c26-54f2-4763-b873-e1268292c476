<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baojia.mapper.UserMapper">
    <!-- 基础映射 -->
    <resultMap id="BaseResultMap" type="com.baojia.entity.User">
        <id column="id" property="id"/>
        <result column="phone" property="phone"/>
        <result column="password" property="password"/>
        <result column="nickname" property="nickname"/>
        <result column="avatar" property="avatar"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
</mapper> 