<template>
  <view class="edit-container">
    <view class="form-box">
      <view class="form-item">
        <text class="label">分类名称</text>
        <input 
          type="text" 
          v-model="form.name" 
          placeholder="请输入分类名称"
          maxlength="50"
        />
      </view>
      
      <view class="form-item">
        <text class="label">分类描述</text>
        <textarea 
          v-model="form.description" 
          placeholder="请输入分类描述"
          maxlength="200"
        />
      </view>
      
      <view class="form-item">
        <text class="label">排序</text>
        <input 
          type="number" 
          v-model="form.sort" 
          placeholder="请输入排序号"
        />
      </view>
      
      <view class="form-item">
        <text class="label">状态</text>
        <switch 
          :checked="form.enabled" 
          @change="handleStatusChange"
          color="#007AFF"
        />
      </view>
      
      <view class="btn-group">
        <button class="btn save" @click="handleSave">保存</button>
        <button class="btn cancel" @click="handleCancel">取消</button>
      </view>
    </view>
  </view>
</template>

<script>
import { createCategory, updateCategory, getCategory } from '../../../api/category'

export default {
  data() {
    return {
      id: null,
      form: {
        name: '',
        description: '',
        sort: 0,
        enabled: true
      }
    }
  },
  
  onLoad(options) {
    if (options.id) {
      this.id = options.id
      this.loadCategory()
    }
  },
  
  methods: {
    async loadCategory() {
      try {
        const res = await getCategory(this.id)
        this.form = res.data
      } catch (error) {
        uni.showToast({
          title: '加载分类信息失败',
          icon: 'none'
        })
      }
    },
    
    handleStatusChange(e) {
      this.form.enabled = e.detail.value
    },
    
    async handleSave() {
      if (!this.form.name) {
        uni.showToast({
          title: '请输入分类名称',
          icon: 'none'
        })
        return
      }
      
      try {
        if (this.id) {
          await updateCategory(this.id, this.form)
        } else {
          await createCategory(this.form)
        }
        
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })
        
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      } catch (error) {
        uni.showToast({
          title: error.message || '保存失败',
          icon: 'none'
        })
      }
    },
    
    handleCancel() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss">
.edit-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
  
  .form-box {
    background-color: #fff;
    border-radius: 10rpx;
    padding: 30rpx;
    
    .form-item {
      margin-bottom: 30rpx;
      
      .label {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 10rpx;
      }
      
      input {
        width: 100%;
        height: 80rpx;
        padding: 0 20rpx;
        border: 2rpx solid #ddd;
        border-radius: 6rpx;
        font-size: 28rpx;
        box-sizing: border-box;
      }
      
      textarea {
        width: 100%;
        height: 200rpx;
        padding: 20rpx;
        border: 2rpx solid #ddd;
        border-radius: 6rpx;
        font-size: 28rpx;
        box-sizing: border-box;
      }
    }
    
    .btn-group {
      margin-top: 40rpx;
      display: flex;
      justify-content: space-between;
      
      .btn {
        width: 45%;
        height: 80rpx;
        line-height: 80rpx;
        text-align: center;
        border-radius: 6rpx;
        font-size: 32rpx;
        
        &.save {
          background-color: #007AFF;
          color: #fff;
        }
        
        &.cancel {
          background-color: #f5f5f5;
          color: #666;
        }
      }
    }
  }
}
</style> 