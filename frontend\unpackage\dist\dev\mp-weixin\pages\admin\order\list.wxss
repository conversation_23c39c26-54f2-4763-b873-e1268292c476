
.container {
  padding: 20rpx;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.search-box {
  display: flex;
  flex: 1;
  margin-right: 20rpx;
}
.search-input {
  flex: 1;
  height: 72rpx;
  background: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
}
.search-btn {
  width: 120rpx;
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  background: #2979ff;
  color: #fff;
  border-radius: 36rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
}
.filter-box {
  width: 200rpx;
}
.status-picker {
  width: 100%;
  height: 72rpx;
  background: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 30rpx;
}
.picker-text {
  line-height: 72rpx;
  font-size: 28rpx;
  color: #333;
}
.order-list {
  display: flex;
  flex-direction: column;
}
.order-item {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.order-no {
  font-size: 28rpx;
  color: #666;
}
.order-status {
  font-size: 28rpx;
  padding: 4rpx 16rpx;
  border-radius: 24rpx;
}
.pending {
  color: #ff9900;
  background: #fff9e6;
}
.processing {
  color: #2979ff;
  background: #e6f3ff;
}
.completed {
  color: #19be6b;
  background: #e6fff0;
}
.cancelled {
  color: #999;
  background: #f5f5f5;
}
.order-content {
  border-top: 1rpx solid #eee;
  border-bottom: 1rpx solid #eee;
  padding: 20rpx 0;
}
.product-info {
  display: flex;
  margin-bottom: 20rpx;
}
.product-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}
.product-detail {
  flex: 1;
}
.product-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}
.product-spec {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}
.product-price {
  font-size: 32rpx;
  color: #ff6b6b;
}
.order-info {
  display: flex;
  flex-direction: column;
}
.info-item {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
}
.total-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff6b6b;
}
.action-btns {
  display: flex;
}
.action-btn {
  width: 120rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  background: #2979ff;
  color: #fff;
  border-radius: 30rpx;
  font-size: 24rpx;
  margin-left: 20rpx;
}
.cancel {
  background: #ff6b6b;
}
