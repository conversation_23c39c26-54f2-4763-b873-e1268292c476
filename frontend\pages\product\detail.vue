<template>
  <view class="container">
    <!-- 产品轮播图 -->
    <u-swiper
      :list="product.images"
      :height="500"
      :radius="0"
      :indicator="true"
    ></u-swiper>

    <!-- 产品信息 -->
    <view class="product-info">
      <view class="price-box">
        <text class="price">¥{{ product.price }}</text>
        <text class="unit">/{{ product.unit }}</text>
      </view>
      <view class="name">{{ product.name }}</view>
      <view class="desc">{{ product.description }}</view>
    </view>

    <!-- 产品规格 -->
    <view class="spec-box">
      <view class="title">产品规格</view>
      <view class="spec-list">
        <view class="spec-item" v-for="(spec, index) in product.specs" :key="index">
          <text class="label">{{ spec.label }}</text>
          <text class="value">{{ spec.value }}</text>
        </view>
      </view>
    </view>

    <!-- 产品视频 -->
    <view class="video-box" v-if="product.video">
      <view class="title">产品视频</view>
      <video
        :src="product.video"
        controls
        class="video"
      ></video>
    </view>

    <!-- 底部导航 -->
    <view class="bottom-nav">
      <view class="nav-item" @click="goToHome">
        <u-icon name="home" size="40"></u-icon>
        <text>首页</text>
      </view>
      <view class="nav-item" @click="goToUser">
        <u-icon name="account" size="40"></u-icon>
        <text>我的</text>
      </view>
      <view class="contact-btn" @click="contactService">
        联系客服
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      product: {
        id: 1,
        name: '高清网络摄像头',
        description: '1080P高清画质，夜视功能，支持手机远程查看',
        price: 299,
        unit: '台',
        images: [
          '/static/product/camera1.jpg',
          '/static/product/camera2.jpg',
          '/static/product/camera3.jpg'
        ],
        specs: [
          { label: '分辨率', value: '1080P' },
          { label: '夜视距离', value: '30米' },
          { label: '防水等级', value: 'IP66' },
          { label: '存储方式', value: '支持TF卡/云存储' }
        ],
        video: '/static/product/camera.mp4'
      }
    }
  },
  onLoad(options) {
    // TODO: 根据id获取产品详情
    console.log('产品ID：', options.id)
  },
  methods: {
    goToHome() {
      uni.switchTab({
        url: '/pages/index/index'
      })
    },
    goToUser() {
      uni.switchTab({
        url: '/pages/user/index'
      })
    },
    contactService() {
      uni.makePhoneCall({
        phoneNumber: '************'
      })
    }
  }
}
</script>

<style lang="scss">
.container {
  padding-bottom: 100rpx;
}

.product-info {
  padding: 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;

  .price-box {
    margin-bottom: 20rpx;

    .price {
      font-size: 48rpx;
      color: #ff6b6b;
      font-weight: bold;
    }

    .unit {
      font-size: 24rpx;
      color: #999;
      margin-left: 4rpx;
    }
  }

  .name {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }

  .desc {
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
  }
}

.spec-box {
  padding: 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;

  .title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }

  .spec-list {
    .spec-item {
      display: flex;
      margin-bottom: 20rpx;

      .label {
        width: 160rpx;
        color: #999;
      }

      .value {
        flex: 1;
        color: #333;
      }
    }
  }
}

.video-box {
  padding: 30rpx;
  background-color: #fff;

  .title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }

  .video {
    width: 100%;
    height: 400rpx;
  }
}

.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

  .nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 60rpx;

    text {
      font-size: 24rpx;
      color: #666;
      margin-top: 4rpx;
    }
  }

  .contact-btn {
    flex: 1;
    height: 80rpx;
    background-color: #2979ff;
    color: #fff;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
  }
}
</style> 