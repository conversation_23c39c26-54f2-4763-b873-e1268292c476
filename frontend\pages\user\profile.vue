<template>
  <view class="profile-container">
    <view class="form">
      <view class="avatar-group">
        <image 
          class="avatar" 
          :src="form.avatar || '/static/default-avatar.png'" 
          mode="aspectFill"
          @click="chooseAvatar"
        />
        <text class="tip">点击更换头像</text>
      </view>
      
      <view class="input-group">
        <text class="label">昵称</text>
        <input 
          type="text" 
          v-model="form.nickname" 
          placeholder="请输入昵称"
          maxlength="20"
        />
      </view>
      
      <view class="input-group">
        <text class="label">真实姓名</text>
        <input 
          type="text" 
          v-model="form.realName" 
          placeholder="请输入真实姓名"
          maxlength="20"
        />
      </view>
      
      <view class="input-group">
        <text class="label">邮箱</text>
        <input 
          type="text" 
          v-model="form.email" 
          placeholder="请输入邮箱"
        />
      </view>
      
      <view class="input-group">
        <text class="label">地址</text>
        <input 
          type="text" 
          v-model="form.address" 
          placeholder="请输入地址"
        />
      </view>
      
      <button 
        class="save-btn" 
        :disabled="!canSave"
        @click="handleSave"
      >保存</button>
    </view>
  </view>
</template>

<script>
import { updateCurrentUser } from '@/api/user'

export default {
  data() {
    return {
      form: {
        avatar: '',
        nickname: '',
        realName: '',
        email: '',
        address: ''
      }
    }
  },
  computed: {
    canSave() {
      return this.form.nickname || 
             this.form.realName || 
             this.form.email || 
             this.form.address ||
             this.form.avatar
    }
  },
  onLoad() {
    // 获取当前用户信息
    const userInfo = uni.getStorageSync('userInfo')
    if (userInfo) {
      this.form = {
        avatar: userInfo.avatar,
        nickname: userInfo.nickname,
        realName: userInfo.realName,
        email: userInfo.email,
        address: userInfo.address
      }
    }
  },
  methods: {
    chooseAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0]
          // TODO: 上传图片到服务器
          this.form.avatar = tempFilePath
        }
      })
    },
    async handleSave() {
      if (!this.canSave) return
      
      try {
        const updated = await updateCurrentUser(this.form)
        
        // 更新本地存储的用户信息
        const userInfo = uni.getStorageSync('userInfo')
        uni.setStorageSync('userInfo', {
          ...userInfo,
          ...updated
        })
        
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })
        
        // 返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      } catch (error) {
        uni.showToast({
          title: error.message || '保存失败，请重试',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style lang="scss">
.profile-container {
  min-height: 100vh;
  padding: 40rpx;
  background-color: #fff;
  
  .form {
    .avatar-group {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 40rpx;
      
      .avatar {
        width: 160rpx;
        height: 160rpx;
        border-radius: 50%;
        margin-bottom: 20rpx;
      }
      
      .tip {
        font-size: 24rpx;
        color: #999;
      }
    }
    
    .input-group {
      margin-bottom: 30rpx;
      
      .label {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 10rpx;
        display: block;
      }
      
      input {
        width: 100%;
        height: 88rpx;
        background-color: #f5f5f5;
        border-radius: 44rpx;
        padding: 0 40rpx;
        font-size: 28rpx;
      }
    }
    
    .save-btn {
      width: 100%;
      height: 88rpx;
      background-color: #2979ff;
      color: #fff;
      font-size: 32rpx;
      border-radius: 44rpx;
      margin-top: 60rpx;
      
      &[disabled] {
        background-color: #ccc;
      }
    }
  }
}
</style> 