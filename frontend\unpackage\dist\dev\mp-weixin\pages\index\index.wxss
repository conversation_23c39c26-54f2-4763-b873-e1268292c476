
.container {
		padding: 20rpx;
}
.banner {
		height: 300rpx;
		border-radius: 12rpx;
		overflow: hidden;
		margin-bottom: 30rpx;
}
.banner-image {
		width: 100%;
		height: 100%;
}
.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
}
.category-section {
		margin-bottom: 30rpx;
}
.category-grid {
		display: flex;
		justify-content: space-between;
		background: #fff;
		border-radius: 12rpx;
		padding: 20rpx;
}
.category-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 33.33%;
}
.category-icon {
		width: 80rpx;
		height: 80rpx;
		margin-bottom: 10rpx;
}
.category-name {
		font-size: 26rpx;
		color: #666;
}
.recommend-section {
		margin-bottom: 30rpx;
}
.product-list {
		display: flex;
		flex-direction: column;
}
.product-item {
		display: flex;
		background: #fff;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
}
.product-image {
		width: 200rpx;
		height: 200rpx;
		border-radius: 8rpx;
		margin-right: 20rpx;
}
.product-info {
		flex: 1;
		display: flex;
		flex-direction: column;
}
.product-name {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 10rpx;
}
.product-desc {
		font-size: 26rpx;
		color: #666;
		margin-bottom: 20rpx;
}
.product-price {
		margin-top: auto;
}
.price {
		font-size: 36rpx;
		color: #ff6b6b;
		font-weight: bold;
}
.unit {
		font-size: 24rpx;
		color: #999;
		margin-left: 4rpx;
}
