"use strict";
const utils_request = require("../utils/request.js");
function getCategoryList() {
  return utils_request.request({
    url: "/category",
    method: "GET"
  });
}
function getCategory(id) {
  return utils_request.request({
    url: `/category/${id}`,
    method: "GET"
  });
}
function createCategory(data) {
  return utils_request.request({
    url: "/category",
    method: "POST",
    data
  });
}
function updateCategory(id, data) {
  return utils_request.request({
    url: `/category/${id}`,
    method: "PUT",
    data
  });
}
exports.createCategory = createCategory;
exports.getCategory = getCategory;
exports.getCategoryList = getCategoryList;
exports.updateCategory = updateCategory;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/category.js.map
