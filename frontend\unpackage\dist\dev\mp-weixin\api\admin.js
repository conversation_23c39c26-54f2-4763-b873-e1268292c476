"use strict";
const utils_request = require("../utils/request.js");
function login(data) {
  return utils_request.request({
    url: "/admin/login",
    method: "POST",
    data
  });
}
function getCategories(keyword) {
  return utils_request.request({
    url: "/admin/categories",
    method: "GET",
    data: { keyword }
  });
}
function deleteCategory(id) {
  return utils_request.request({
    url: `/admin/categories/${id}`,
    method: "DELETE"
  });
}
function getProducts(keyword, categoryId) {
  return utils_request.request({
    url: "/admin/products",
    method: "GET",
    data: { keyword, categoryId }
  });
}
function deleteProduct(id) {
  return utils_request.request({
    url: `/admin/products/${id}`,
    method: "DELETE"
  });
}
function getOrders(keyword, status) {
  return utils_request.request({
    url: "/admin/orders",
    method: "GET",
    data: { keyword, status }
  });
}
function updateOrderStatus(id, status, handleRemark) {
  return utils_request.request({
    url: `/admin/orders/${id}/status`,
    method: "PUT",
    data: { status, handleRemark }
  });
}
exports.deleteCategory = deleteCategory;
exports.deleteProduct = deleteProduct;
exports.getCategories = getCategories;
exports.getOrders = getOrders;
exports.getProducts = getProducts;
exports.login = login;
exports.updateOrderStatus = updateOrderStatus;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/admin.js.map
