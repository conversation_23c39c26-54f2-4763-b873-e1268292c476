
.container {
  padding: 20rpx;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.search-box {
  display: flex;
  flex: 1;
  margin-right: 20rpx;
}
.search-input {
  flex: 1;
  height: 72rpx;
  background: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
}
.search-btn {
  width: 120rpx;
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  background: #2979ff;
  color: #fff;
  border-radius: 36rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
}
.add-btn {
  width: 160rpx;
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  background: #19be6b;
  color: #fff;
  border-radius: 36rpx;
  font-size: 28rpx;
}
.category-list {
  display: flex;
  flex-direction: column;
}
.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}
.category-info {
  flex: 1;
}
.category-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}
.category-desc {
  font-size: 26rpx;
  color: #666;
}
.category-actions {
  display: flex;
  flex-direction: column;
  margin-left: 20rpx;
}
.action-btn {
  width: 120rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  border-radius: 30rpx;
  font-size: 24rpx;
  margin-bottom: 10rpx;
}
.edit {
  background: #2979ff;
  color: #fff;
}
.delete {
  background: #ff6b6b;
  color: #fff;
}
