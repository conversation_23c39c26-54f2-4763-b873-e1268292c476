package com.baojia.controller;

import com.baojia.entity.User;
import com.baojia.model.LoginRequest;
import com.baojia.model.Result;
import com.baojia.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/user")
public class UserController {
    @Autowired
    private UserService userService;

    @PostMapping("/login")
    public Result<String> login(@RequestBody LoginRequest request) {
        String token = userService.login(request);
        return Result.success(token);
    }

    @PostMapping("/register")
    public Result<User> register(@RequestBody User user) {
        User registered = userService.register(user);
        return Result.success(registered);
    }

    @PostMapping("/send-code")
    public Result<Void> sendCode(@RequestParam String phone) {
        userService.sendCode(phone);
        return Result.success();
    }

    @GetMapping("/current")
    public Result<User> getCurrentUser() {
        User user = userService.getCurrentUser();
        return Result.success(user);
    }

    @PutMapping("/current")
    public Result<User> updateCurrentUser(@RequestBody User user) {
        User updated = userService.updateCurrentUser(user);
        return Result.success(updated);
    }
} 