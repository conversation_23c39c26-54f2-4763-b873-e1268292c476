"use strict";
const common_vendor = require("../../../common/vendor.js");
const api_order = require("../../../api/order.js");
const _sfc_main = {
  data() {
    return {
      id: null,
      order: {},
      handleRemark: ""
    };
  },
  onLoad(options) {
    if (options.id) {
      this.id = options.id;
      this.loadOrder();
    }
  },
  methods: {
    async loadOrder() {
      try {
        const res = await api_order.getOrderDetail(this.id);
        this.order = res.data;
      } catch (error) {
        common_vendor.index.showToast({
          title: "加载订单信息失败",
          icon: "none"
        });
      }
    },
    getStatusText(status) {
      const statusMap = {
        "PENDING": "待处理",
        "ACCEPTED": "已接受",
        "REJECTED": "已拒绝",
        "COMPLETED": "已完成",
        "CANCELLED": "已取消"
      };
      return statusMap[status] || status;
    },
    async handleOrder(status) {
      if (!this.handleRemark) {
        common_vendor.index.showToast({
          title: "请输入处理备注",
          icon: "none"
        });
        return;
      }
      try {
        await api_order.updateOrderStatus(this.id, status, this.handleRemark);
        common_vendor.index.showToast({
          title: "处理成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      } catch (error) {
        common_vendor.index.showToast({
          title: error.message || "处理失败",
          icon: "none"
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.order.orderNo),
    b: common_vendor.t($options.getStatusText($data.order.status)),
    c: common_vendor.t($data.order.createTime),
    d: common_vendor.t($data.order.userName),
    e: common_vendor.t($data.order.userPhone),
    f: common_vendor.t($data.order.address),
    g: common_vendor.t($data.order.totalAmount),
    h: common_vendor.f($data.order.items, (item, index, i0) => {
      return {
        a: item.productImage,
        b: common_vendor.t(item.productName),
        c: common_vendor.t(item.price),
        d: common_vendor.t(item.quantity),
        e: index
      };
    }),
    i: $data.order.status === "PENDING"
  }, $data.order.status === "PENDING" ? {
    j: $data.handleRemark,
    k: common_vendor.o(($event) => $data.handleRemark = $event.detail.value),
    l: common_vendor.o(($event) => $options.handleOrder("ACCEPTED")),
    m: common_vendor.o(($event) => $options.handleOrder("REJECTED"))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/admin/order/detail.js.map
