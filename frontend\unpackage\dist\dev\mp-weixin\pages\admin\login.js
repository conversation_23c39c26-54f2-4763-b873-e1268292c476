"use strict";
const common_vendor = require("../../common/vendor.js");
const api_admin = require("../../api/admin.js");
const _sfc_main = {
  data() {
    return {
      form: {
        phone: "",
        password: ""
      }
    };
  },
  methods: {
    // 手机号密码登录
    async handleLogin() {
      if (!this.form.phone) {
        common_vendor.index.showToast({
          title: "请输入手机号",
          icon: "none"
        });
        return;
      }
      if (!this.form.password) {
        common_vendor.index.showToast({
          title: "请输入密码",
          icon: "none"
        });
        return;
      }
      try {
        const res = await api_admin.login(this.form);
        common_vendor.index.setStorageSync("token", res.token);
        common_vendor.index.reLaunch({
          url: "/pages/admin/index"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/admin/login.vue:77", "登录失败:", error);
      }
    },
    // 微信登录
    handleWechatLogin() {
    },
    // 获取微信用户信息
    getWechatUserInfo() {
      return new Promise((resolve, reject) => {
        common_vendor.index.getUserInfo({
          provider: "weixin",
          success: (res) => {
            resolve(res.userInfo);
          },
          fail: (err) => {
            reject(err);
          }
        });
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $data.form.phone,
    b: common_vendor.o(($event) => $data.form.phone = $event.detail.value),
    c: $data.form.password,
    d: common_vendor.o(($event) => $data.form.password = $event.detail.value),
    e: common_vendor.o((...args) => $options.handleLogin && $options.handleLogin(...args)),
    f: common_vendor.o((...args) => $options.handleWechatLogin && $options.handleWechatLogin(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/admin/login.js.map
