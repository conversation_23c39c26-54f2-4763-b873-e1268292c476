"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      product: {
        id: 1,
        name: "高清网络摄像头",
        description: "1080P高清画质，夜视功能，支持手机远程查看",
        price: 299,
        unit: "台",
        images: [
          "/static/product/camera1.jpg",
          "/static/product/camera2.jpg",
          "/static/product/camera3.jpg"
        ],
        specs: [
          { label: "分辨率", value: "1080P" },
          { label: "夜视距离", value: "30米" },
          { label: "防水等级", value: "IP66" },
          { label: "存储方式", value: "支持TF卡/云存储" }
        ],
        video: "/static/product/camera.mp4"
      }
    };
  },
  onLoad(options) {
    common_vendor.index.__f__("log", "at pages/product/detail.vue:86", "产品ID：", options.id);
  },
  methods: {
    goToHome() {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    },
    goToUser() {
      common_vendor.index.switchTab({
        url: "/pages/user/index"
      });
    },
    contactService() {
      common_vendor.index.makePhoneCall({
        phoneNumber: "************"
      });
    }
  }
};
if (!Array) {
  const _component_u_swiper = common_vendor.resolveComponent("u-swiper");
  const _component_u_icon = common_vendor.resolveComponent("u-icon");
  (_component_u_swiper + _component_u_icon)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      list: $data.product.images,
      height: 500,
      radius: 0,
      indicator: true
    }),
    b: common_vendor.t($data.product.price),
    c: common_vendor.t($data.product.unit),
    d: common_vendor.t($data.product.name),
    e: common_vendor.t($data.product.description),
    f: common_vendor.f($data.product.specs, (spec, index, i0) => {
      return {
        a: common_vendor.t(spec.label),
        b: common_vendor.t(spec.value),
        c: index
      };
    }),
    g: $data.product.video
  }, $data.product.video ? {
    h: $data.product.video
  } : {}, {
    i: common_vendor.p({
      name: "home",
      size: "40"
    }),
    j: common_vendor.o((...args) => $options.goToHome && $options.goToHome(...args)),
    k: common_vendor.p({
      name: "account",
      size: "40"
    }),
    l: common_vendor.o((...args) => $options.goToUser && $options.goToUser(...args)),
    m: common_vendor.o((...args) => $options.contactService && $options.contactService(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/product/detail.js.map
