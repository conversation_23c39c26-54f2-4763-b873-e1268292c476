<template>
  <view class="container">
    <!-- 服务类型选择 -->
    <view class="service-types">
      <view
        class="type-item"
        v-for="(type, index) in serviceTypes"
        :key="index"
        :class="{ active: selectedType === index }"
        @click="selectType(index)"
      >
        <u-icon :name="type.icon" size="60" :color="selectedType === index ? '#2979ff' : '#666'"></u-icon>
        <text>{{ type.name }}</text>
      </view>
    </view>

    <!-- 问题描述 -->
    <view class="problem-form">
      <view class="form-title">问题描述</view>
      <view class="form-item">
        <text class="label">联系电话</text>
        <u-input
          v-model="form.phone"
          placeholder="请输入您的联系电话"
          type="number"
        ></u-input>
      </view>
      <view class="form-item">
        <text class="label">问题描述</text>
        <u-textarea
          v-model="form.description"
          placeholder="请详细描述您遇到的问题"
          height="200"
        ></u-textarea>
      </view>
    </view>

    <!-- 快速联系 -->
    <view class="quick-contact">
      <view class="contact-title">快速联系</view>
      <view class="phone-list">
        <view class="phone-item" v-for="(phone, index) in phoneNumbers" :key="index">
          <text class="name">{{ phone.name }}</text>
          <text class="number">{{ phone.number }}</text>
          <u-button
            type="primary"
            size="mini"
            @click="makePhoneCall(phone.number)"
          >拨打</u-button>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-btn" @click="submitForm">
      提交
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      selectedType: 0,
      serviceTypes: [
        { name: '安装服务', icon: 'setting' },
        { name: '维修服务', icon: 'wrench' },
        { name: '咨询服务', icon: 'chat' }
      ],
      form: {
        phone: '',
        description: ''
      },
      phoneNumbers: [
        { name: '安装服务', number: '************' },
        { name: '维修服务', number: '************' },
        { name: '咨询服务', number: '************' }
      ]
    }
  },
  methods: {
    selectType(index) {
      this.selectedType = index
    },
    makePhoneCall(number) {
      uni.makePhoneCall({
        phoneNumber: number
      })
    },
    submitForm() {
      if (!this.form.phone) {
        uni.showToast({
          title: '请输入联系电话',
          icon: 'none'
        })
        return
      }
      if (!this.form.description) {
        uni.showToast({
          title: '请输入问题描述',
          icon: 'none'
        })
        return
      }
      // TODO: 提交表单
      uni.showLoading({
        title: '提交中...'
      })
      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({
          title: '提交成功',
          icon: 'success'
        })
      }, 1500)
    }
  }
}
</script>

<style lang="scss">
.container {
  padding: 20rpx;
}

.service-types {
  display: flex;
  justify-content: space-around;
  background-color: #fff;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

  .type-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20rpx;
    border-radius: 8rpx;
    transition: all 0.3s;

    &.active {
      background-color: #f0f7ff;
    }

    text {
      font-size: 28rpx;
      color: #666;
      margin-top: 10rpx;
    }
  }
}

.problem-form {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

  .form-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
  }

  .form-item {
    margin-bottom: 30rpx;

    .label {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 10rpx;
      display: block;
    }
  }
}

.quick-contact {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

  .contact-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
  }

  .phone-list {
    .phone-item {
      display: flex;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #eee;

      &:last-child {
        border-bottom: none;
      }

      .name {
        font-size: 28rpx;
        color: #333;
        width: 160rpx;
      }

      .number {
        flex: 1;
        font-size: 28rpx;
        color: #666;
      }
    }
  }
}

.submit-btn {
  position: fixed;
  bottom: 40rpx;
  left: 40rpx;
  right: 40rpx;
  height: 88rpx;
  background-color: #2979ff;
  color: #fff;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(41, 121, 255, 0.4);
}
</style> 