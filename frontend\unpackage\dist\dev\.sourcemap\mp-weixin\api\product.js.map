{"version": 3, "file": "product.js", "sources": ["api/product.js"], "sourcesContent": ["import request from '../utils/request'\r\n\r\n// 获取产品列表\r\nexport function getProductList(params) {\r\n  return request({\r\n    url: '/product',\r\n    method: 'GET',\r\n    data: params\r\n  })\r\n}\r\n\r\n// 获取产品详情\r\nexport function getProductDetail(id) {\r\n  return request({\r\n    url: `/product/${id}`,\r\n    method: 'GET'\r\n  })\r\n}\r\n\r\n// 创建产品\r\nexport function createProduct(data) {\r\n  return request({\r\n    url: '/product',\r\n    method: 'POST',\r\n    data\r\n  })\r\n}\r\n\r\n// 更新产品\r\nexport function updateProduct(id, data) {\r\n  return request({\r\n    url: `/product/${id}`,\r\n    method: 'PUT',\r\n    data\r\n  })\r\n}\r\n\r\n// 删除产品\r\nexport function deleteProduct(id) {\r\n  return request({\r\n    url: `/product/${id}`,\r\n    method: 'DELETE'\r\n  })\r\n}\r\n\r\n// 更新产品状态\r\nexport function updateProductStatus(id, enabled) {\r\n  return request({\r\n    url: `/product/${id}/status`,\r\n    method: 'PUT',\r\n    data: { enabled }\r\n  })\r\n}\r\n\r\n// 上传产品图片\r\nexport function uploadProductImage(file) {\r\n  return request({\r\n    url: '/file/upload',\r\n    method: 'POST',\r\n    header: {\r\n      'Content-Type': 'multipart/form-data'\r\n    },\r\n    data: file\r\n  })\r\n} "], "names": ["request"], "mappings": ";;AAYO,SAAS,iBAAiB,IAAI;AACnC,SAAOA,sBAAQ;AAAA,IACb,KAAK,YAAY,EAAE;AAAA,IACnB,QAAQ;AAAA,EACZ,CAAG;AACH;AAGO,SAAS,cAAc,MAAM;AAClC,SAAOA,sBAAQ;AAAA,IACb,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACJ,CAAG;AACH;AAGO,SAAS,cAAc,IAAI,MAAM;AACtC,SAAOA,sBAAQ;AAAA,IACb,KAAK,YAAY,EAAE;AAAA,IACnB,QAAQ;AAAA,IACR;AAAA,EACJ,CAAG;AACH;AAoBO,SAAS,mBAAmB,MAAM;AACvC,SAAOA,sBAAQ;AAAA,IACb,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,QAAQ;AAAA,MACN,gBAAgB;AAAA,IACjB;AAAA,IACD,MAAM;AAAA,EACV,CAAG;AACH;;;;;"}