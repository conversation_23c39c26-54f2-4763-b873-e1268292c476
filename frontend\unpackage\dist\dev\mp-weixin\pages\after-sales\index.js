"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      selectedType: 0,
      serviceTypes: [
        { name: "安装服务", icon: "setting" },
        { name: "维修服务", icon: "wrench" },
        { name: "咨询服务", icon: "chat" }
      ],
      form: {
        phone: "",
        description: ""
      },
      phoneNumbers: [
        { name: "安装服务", number: "************" },
        { name: "维修服务", number: "************" },
        { name: "咨询服务", number: "************" }
      ]
    };
  },
  methods: {
    selectType(index) {
      this.selectedType = index;
    },
    makePhoneCall(number) {
      common_vendor.index.makePhoneCall({
        phoneNumber: number
      });
    },
    submitForm() {
      if (!this.form.phone) {
        common_vendor.index.showToast({
          title: "请输入联系电话",
          icon: "none"
        });
        return;
      }
      if (!this.form.description) {
        common_vendor.index.showToast({
          title: "请输入问题描述",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "提交成功",
          icon: "success"
        });
      }, 1500);
    }
  }
};
if (!Array) {
  const _component_u_icon = common_vendor.resolveComponent("u-icon");
  const _component_u_input = common_vendor.resolveComponent("u-input");
  const _component_u_textarea = common_vendor.resolveComponent("u-textarea");
  const _component_u_button = common_vendor.resolveComponent("u-button");
  (_component_u_icon + _component_u_input + _component_u_textarea + _component_u_button)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($data.serviceTypes, (type, index, i0) => {
      return {
        a: "5e0ad592-0-" + i0,
        b: common_vendor.p({
          name: type.icon,
          size: "60",
          color: $data.selectedType === index ? "#2979ff" : "#666"
        }),
        c: common_vendor.t(type.name),
        d: index,
        e: $data.selectedType === index ? 1 : "",
        f: common_vendor.o(($event) => $options.selectType(index), index)
      };
    }),
    b: common_vendor.o(($event) => $data.form.phone = $event),
    c: common_vendor.p({
      placeholder: "请输入您的联系电话",
      type: "number",
      modelValue: $data.form.phone
    }),
    d: common_vendor.o(($event) => $data.form.description = $event),
    e: common_vendor.p({
      placeholder: "请详细描述您遇到的问题",
      height: "200",
      modelValue: $data.form.description
    }),
    f: common_vendor.f($data.phoneNumbers, (phone, index, i0) => {
      return {
        a: common_vendor.t(phone.name),
        b: common_vendor.t(phone.number),
        c: common_vendor.o(($event) => $options.makePhoneCall(phone.number), index),
        d: "5e0ad592-3-" + i0,
        e: index
      };
    }),
    g: common_vendor.p({
      type: "primary",
      size: "mini"
    }),
    h: common_vendor.o((...args) => $options.submitForm && $options.submitForm(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/after-sales/index.js.map
