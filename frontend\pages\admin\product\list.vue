<template>
  <view class="product-list">
    <view class="header">
      <view class="title">产品管理</view>
      <button class="add-btn" @click="navigateTo('/pages/admin/product/edit')">添加产品</button>
    </view>
    
    <view class="search-box">
      <input type="text" v-model="searchForm.keyword" placeholder="请输入产品名称" />
      <button @click="handleSearch">搜索</button>
    </view>
    
    <view class="list">
      <view class="item" v-for="item in list" :key="item.id">
        <image class="image" :src="item.image" mode="aspectFill" />
        <view class="info">
          <view class="name">{{ item.name }}</view>
          <view class="price">¥{{ item.price }}</view>
          <view class="stock">库存: {{ item.stock }}</view>
        </view>
        <view class="actions">
          <button class="edit-btn" @click="navigateTo(`/pages/admin/product/edit?id=${item.id}`)">编辑</button>
          <button class="delete-btn" @click="handleDelete(item.id)">删除</button>
        </view>
      </view>
    </view>
    
    <view class="empty" v-if="list.length === 0">
      暂无数据
    </view>
  </view>
</template>

<script>
import { getProductList, deleteProduct } from '@/api/admin'

export default {
  data() {
    return {
      list: [],
      searchForm: {
        keyword: ''
      }
    }
  },
  onLoad() {
    this.getList()
  },
  methods: {
    async getList() {
      try {
        this.list = await getProductList(this.searchForm)
      } catch (error) {
        console.error('获取产品列表失败:', error)
      }
    },
    handleSearch() {
      this.getList()
    },
    navigateTo(url) {
      uni.navigateTo({ url })
    },
    async handleDelete(id) {
      try {
        await uni.showModal({
          title: '提示',
          content: '确定要删除该产品吗？'
        })
        
        await deleteProduct(id)
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
        this.getList()
      } catch (error) {
        console.error('删除产品失败:', error)
      }
    }
  }
}
</script>

<style lang="scss">
.product-list {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .title {
      font-size: 32rpx;
      font-weight: bold;
    }
    
    .add-btn {
      background-color: #2979ff;
      color: #fff;
      font-size: 28rpx;
      padding: 10rpx 30rpx;
      border-radius: 8rpx;
    }
  }
  
  .search-box {
    display: flex;
    margin-bottom: 20rpx;
    
    input {
      flex: 1;
      height: 70rpx;
      background-color: #fff;
      padding: 0 20rpx;
      border-radius: 8rpx;
      margin-right: 20rpx;
    }
    
    button {
      width: 160rpx;
      height: 70rpx;
      line-height: 70rpx;
      background-color: #2979ff;
      color: #fff;
      font-size: 28rpx;
      border-radius: 8rpx;
    }
  }
  
  .list {
    .item {
      background-color: #fff;
      border-radius: 12rpx;
      padding: 20rpx;
      margin-bottom: 20rpx;
      display: flex;
      
      .image {
        width: 160rpx;
        height: 160rpx;
        border-radius: 8rpx;
        margin-right: 20rpx;
      }
      
      .info {
        flex: 1;
        
        .name {
          font-size: 28rpx;
          font-weight: bold;
          margin-bottom: 10rpx;
        }
        
        .price {
          color: #f56c6c;
          font-size: 32rpx;
          margin-bottom: 10rpx;
        }
        
        .stock {
          color: #666;
          font-size: 24rpx;
        }
      }
      
      .actions {
        display: flex;
        flex-direction: column;
        justify-content: center;
        
        button {
          width: 120rpx;
          height: 60rpx;
          line-height: 60rpx;
          font-size: 24rpx;
          margin-bottom: 10rpx;
          
          &.edit-btn {
            background-color: #2979ff;
            color: #fff;
          }
          
          &.delete-btn {
            background-color: #f56c6c;
            color: #fff;
          }
        }
      }
    }
  }
  
  .empty {
    text-align: center;
    color: #999;
    font-size: 28rpx;
    margin-top: 100rpx;
  }
}
</style> 