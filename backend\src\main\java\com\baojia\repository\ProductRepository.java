package com.baojia.repository;

import com.baojia.entity.Product;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import java.util.List;

public interface ProductRepository extends JpaRepository<Product, Long> {
    @Query("SELECT p FROM Product p WHERE (p.name LIKE %:keyword% OR p.description LIKE %:keyword%) AND p.deleted = 0 AND p.status = 1")
    List<Product> search(@Param("keyword") String keyword);

    @Query("SELECT p FROM Product p WHERE p.deleted = 0 AND p.status = 1 AND p.isRecommend = 1 ORDER BY p.sort ASC")
    List<Product> findAllRecommended();

    @Query("SELECT p FROM Product p WHERE p.deleted = 0 AND p.status = 1 AND p.category.id = :categoryId ORDER BY p.sort ASC")
    List<Product> findByCategoryId(@Param("categoryId") Long categoryId);

    @Query("SELECT p FROM Product p WHERE p.deleted = 0 AND p.status = 1 ORDER BY p.sort ASC")
    List<Product> findAllActive();
} 