package com.baojia.controller;

import com.baojia.entity.Order;
import com.baojia.model.Result;
import com.baojia.service.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/orders")
public class OrderController {
    @Autowired
    private OrderService orderService;

    @GetMapping
    public Result<List<Order>> list(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String status) {
        return Result.success(orderService.list(keyword, status));
    }

    @GetMapping("/{id}")
    public Result<Order> getById(@PathVariable Long id) {
        return Result.success(orderService.getById(id));
    }

    @PostMapping
    public Result<Order> createOrder(@RequestBody Order order) {
        return Result.success(orderService.create(order));
    }

    @PutMapping("/{id}")
    public Result<Order> updateOrder(@PathVariable Long id, @RequestBody Order order) {
        return Result.success(orderService.update(id, order));
    }

    @PutMapping("/{id}/status")
    public Result<Order> updateStatus(
            @PathVariable Long id,
            @RequestParam String status,
            @RequestParam(required = false) String handleRemark) {
        return Result.success(orderService.updateStatus(id, status, handleRemark));
    }

    @DeleteMapping("/{id}")
    public Result<Void> deleteOrder(@PathVariable Long id) {
        orderService.delete(id);
        return Result.success();
    }
} 