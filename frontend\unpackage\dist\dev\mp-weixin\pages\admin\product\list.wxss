
.container {
  padding: 20rpx;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.search-box {
  display: flex;
  flex: 1;
  margin-right: 20rpx;
}
.search-input {
  flex: 1;
  height: 72rpx;
  background: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
}
.search-btn {
  width: 120rpx;
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  background: #2979ff;
  color: #fff;
  border-radius: 36rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
}
.add-btn {
  width: 160rpx;
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  background: #19be6b;
  color: #fff;
  border-radius: 36rpx;
  font-size: 28rpx;
}
.product-list {
  display: flex;
  flex-direction: column;
}
.product-item {
  display: flex;
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}
.product-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}
.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.product-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}
.product-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
}
.product-price {
  margin-top: auto;
}
.price {
  font-size: 36rpx;
  color: #ff6b6b;
  font-weight: bold;
}
.unit {
  font-size: 24rpx;
  color: #999;
  margin-left: 4rpx;
}
.product-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-left: 20rpx;
}
.action-btn {
  width: 120rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  border-radius: 30rpx;
  font-size: 24rpx;
  margin-bottom: 10rpx;
}
.edit {
  background: #2979ff;
  color: #fff;
}
.delete {
  background: #ff6b6b;
  color: #fff;
}
