"use strict";
const common_vendor = require("../../../common/vendor.js");
const api_admin = require("../../../api/admin.js");
const _sfc_main = {
  data() {
    return {
      searchKeyword: "",
      products: []
    };
  },
  onLoad() {
    this.loadProducts();
  },
  methods: {
    loadProducts() {
      api_admin.getProducts({
        keyword: this.searchKeyword
      }).then((res) => {
        this.products = res.data;
      });
    },
    handleSearch() {
      this.loadProducts();
    },
    handleAdd() {
      common_vendor.index.navigateTo({
        url: "/pages/admin/product/edit"
      });
    },
    handleEdit(id) {
      common_vendor.index.navigateTo({
        url: `/pages/admin/product/edit?id=${id}`
      });
    },
    handleDelete(id) {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要删除该产品吗？",
        success: (res) => {
          if (res.confirm) {
            api_admin.deleteProduct(id).then(() => {
              common_vendor.index.showToast({
                title: "删除成功"
              });
              this.loadProducts();
            });
          }
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    b: $data.searchKeyword,
    c: common_vendor.o(($event) => $data.searchKeyword = $event.detail.value),
    d: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    e: common_vendor.o((...args) => $options.handleAdd && $options.handleAdd(...args)),
    f: common_vendor.f($data.products, (item, index, i0) => {
      return {
        a: item.image,
        b: common_vendor.t(item.name),
        c: common_vendor.t(item.description),
        d: common_vendor.t(item.price),
        e: common_vendor.t(item.unit),
        f: common_vendor.o(($event) => $options.handleEdit(item.id), index),
        g: common_vendor.o(($event) => $options.handleDelete(item.id), index),
        h: index
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/admin/product/list.js.map
