package com.baojia.controller;

import com.baojia.entity.Admin;
import com.baojia.model.LoginRequest;
import com.baojia.model.Result;
import com.baojia.service.AdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin")
public class AdminController {
    @Autowired
    private AdminService adminService;

    @PostMapping("/login")
    public Result<String> login(@RequestBody LoginRequest request) {
        return Result.success(adminService.login(request));
    }

    @PostMapping("/register")
    public Result<String> register(@RequestBody LoginRequest request) {
        return Result.success(adminService.register(request));
    }

    @PostMapping("/sendCode")
    public Result<Void> sendCode(@RequestBody LoginRequest request) {
        adminService.sendCode(request);
        return Result.success();
    }

    @GetMapping("/info")
    public Result<Object> getInfo(@RequestHeader("Authorization") String token) {
        return Result.success(adminService.getInfo(token));
    }

    @PostMapping("/update")
    public Result<Void> update(@RequestHeader("Authorization") String token, @RequestBody LoginRequest request) {
        adminService.update(token, request);
        return Result.success();
    }

    @GetMapping("/current")
    public Result<Admin> getCurrentAdmin() {
        Admin admin = adminService.getCurrentAdmin();
        return Result.success(admin);
    }

    @PostMapping
    public Result<Admin> create(@RequestBody Admin admin) {
        Admin created = adminService.create(admin);
        return Result.success(created);
    }

    @PutMapping("/{id}")
    public Result<Admin> update(@PathVariable Long id, @RequestBody Admin admin) {
        Admin updated = adminService.update(id, admin);
        return Result.success(updated);
    }
    
    @PostMapping("/bind-phone")
    public Result<Admin> bindPhone(@RequestParam String phone, @RequestParam String password) {
        Admin admin = adminService.bindPhone(phone, password);
        return Result.success(admin);
    }
} 