"use strict";
const utils_request = require("../utils/request.js");
function getProductDetail(id) {
  return utils_request.request({
    url: `/product/${id}`,
    method: "GET"
  });
}
function createProduct(data) {
  return utils_request.request({
    url: "/product",
    method: "POST",
    data
  });
}
function updateProduct(id, data) {
  return utils_request.request({
    url: `/product/${id}`,
    method: "PUT",
    data
  });
}
function uploadProductImage(file) {
  return utils_request.request({
    url: "/file/upload",
    method: "POST",
    header: {
      "Content-Type": "multipart/form-data"
    },
    data: file
  });
}
exports.createProduct = createProduct;
exports.getProductDetail = getProductDetail;
exports.updateProduct = updateProduct;
exports.uploadProductImage = uploadProductImage;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/product.js.map
