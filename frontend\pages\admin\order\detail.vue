<template>
  <view class="detail-container">
    <view class="order-info">
      <view class="info-item">
        <text class="label">订单编号</text>
        <text class="value">{{ order.orderNo }}</text>
      </view>
      
      <view class="info-item">
        <text class="label">订单状态</text>
        <text class="value">{{ getStatusText(order.status) }}</text>
      </view>
      
      <view class="info-item">
        <text class="label">下单时间</text>
        <text class="value">{{ order.createTime }}</text>
      </view>
      
      <view class="info-item">
        <text class="label">用户信息</text>
        <text class="value">{{ order.userName }} {{ order.userPhone }}</text>
      </view>
      
      <view class="info-item">
        <text class="label">收货地址</text>
        <text class="value">{{ order.address }}</text>
      </view>
      
      <view class="info-item">
        <text class="label">订单金额</text>
        <text class="value price">¥{{ order.totalAmount }}</text>
      </view>
    </view>
    
    <view class="product-list">
      <view class="title">商品信息</view>
      <view 
        class="product-item" 
        v-for="(item, index) in order.items" 
        :key="index"
      >
        <image :src="item.productImage" mode="aspectFill" />
        <view class="info">
          <text class="name">{{ item.productName }}</text>
          <text class="price">¥{{ item.price }}</text>
          <text class="quantity">x{{ item.quantity }}</text>
        </view>
      </view>
    </view>
    
    <view class="handle-box" v-if="order.status === 'PENDING'">
      <view class="title">处理订单</view>
      <view class="form-item">
        <text class="label">处理备注</text>
        <textarea 
          v-model="handleRemark" 
          placeholder="请输入处理备注"
          maxlength="200"
        />
      </view>
      <view class="btn-group">
        <button class="btn accept" @click="handleOrder('ACCEPTED')">接受订单</button>
        <button class="btn reject" @click="handleOrder('REJECTED')">拒绝订单</button>
      </view>
    </view>
  </view>
</template>

<script>
import { getOrderDetail, updateOrderStatus } from '../../../api/order'

export default {
  data() {
    return {
      id: null,
      order: {},
      handleRemark: ''
    }
  },
  
  onLoad(options) {
    if (options.id) {
      this.id = options.id
      this.loadOrder()
    }
  },
  
  methods: {
    async loadOrder() {
      try {
        const res = await getOrderDetail(this.id)
        this.order = res.data
      } catch (error) {
        uni.showToast({
          title: '加载订单信息失败',
          icon: 'none'
        })
      }
    },
    
    getStatusText(status) {
      const statusMap = {
        'PENDING': '待处理',
        'ACCEPTED': '已接受',
        'REJECTED': '已拒绝',
        'COMPLETED': '已完成',
        'CANCELLED': '已取消'
      }
      return statusMap[status] || status
    },
    
    async handleOrder(status) {
      if (!this.handleRemark) {
        uni.showToast({
          title: '请输入处理备注',
          icon: 'none'
        })
        return
      }
      
      try {
        await updateOrderStatus(this.id, status, this.handleRemark)
        uni.showToast({
          title: '处理成功',
          icon: 'success'
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      } catch (error) {
        uni.showToast({
          title: error.message || '处理失败',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style lang="scss">
.detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
  
  .order-info {
    background-color: #fff;
    border-radius: 10rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    
    .info-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .label {
        color: #666;
        font-size: 28rpx;
      }
      
      .value {
        color: #333;
        font-size: 28rpx;
        
        &.price {
          color: #f00;
          font-weight: bold;
        }
      }
    }
  }
  
  .product-list {
    background-color: #fff;
    border-radius: 10rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    
    .title {
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
    }
    
    .product-item {
      display: flex;
      margin-bottom: 20rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      image {
        width: 160rpx;
        height: 160rpx;
        border-radius: 6rpx;
        margin-right: 20rpx;
      }
      
      .info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        
        .name {
          font-size: 28rpx;
          color: #333;
        }
        
        .price {
          font-size: 28rpx;
          color: #f00;
        }
        
        .quantity {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
  }
  
  .handle-box {
    background-color: #fff;
    border-radius: 10rpx;
    padding: 30rpx;
    
    .title {
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
    }
    
    .form-item {
      margin-bottom: 30rpx;
      
      .label {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 10rpx;
      }
      
      textarea {
        width: 100%;
        height: 200rpx;
        padding: 20rpx;
        border: 2rpx solid #ddd;
        border-radius: 6rpx;
        font-size: 28rpx;
        box-sizing: border-box;
      }
    }
    
    .btn-group {
      display: flex;
      justify-content: space-between;
      
      .btn {
        width: 45%;
        height: 80rpx;
        line-height: 80rpx;
        text-align: center;
        border-radius: 6rpx;
        font-size: 32rpx;
        
        &.accept {
          background-color: #007AFF;
          color: #fff;
        }
        
        &.reject {
          background-color: #f5f5f5;
          color: #666;
        }
      }
    }
  }
}
</style> 