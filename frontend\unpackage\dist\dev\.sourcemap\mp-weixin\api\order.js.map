{"version": 3, "file": "order.js", "sources": ["api/order.js"], "sourcesContent": ["import request from '../utils/request'\r\n\r\n// 获取订单列表\r\nexport function getOrderList(params) {\r\n  return request({\r\n    url: '/order',\r\n    method: 'GET',\r\n    data: params\r\n  })\r\n}\r\n\r\n// 获取订单详情\r\nexport function getOrderDetail(id) {\r\n  return request({\r\n    url: `/order/${id}`,\r\n    method: 'GET'\r\n  })\r\n}\r\n\r\n// 更新订单状态\r\nexport function updateOrderStatus(id, status, handleRemark) {\r\n  return request({\r\n    url: `/order/${id}/status`,\r\n    method: 'PUT',\r\n    data: { status, handleRemark }\r\n  })\r\n}\r\n\r\n// 删除订单\r\nexport function deleteOrder(id) {\r\n  return request({\r\n    url: `/order/${id}`,\r\n    method: 'DELETE'\r\n  })\r\n} "], "names": ["request"], "mappings": ";;AAYO,SAAS,eAAe,IAAI;AACjC,SAAOA,sBAAQ;AAAA,IACb,KAAK,UAAU,EAAE;AAAA,IACjB,QAAQ;AAAA,EACZ,CAAG;AACH;AAGO,SAAS,kBAAkB,IAAI,QAAQ,cAAc;AAC1D,SAAOA,sBAAQ;AAAA,IACb,KAAK,UAAU,EAAE;AAAA,IACjB,QAAQ;AAAA,IACR,MAAM,EAAE,QAAQ,aAAc;AAAA,EAClC,CAAG;AACH;;;"}