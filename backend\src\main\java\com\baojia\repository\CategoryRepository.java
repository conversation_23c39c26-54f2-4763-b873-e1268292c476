package com.baojia.repository;

import com.baojia.entity.Category;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import java.util.List;

public interface CategoryRepository extends JpaRepository<Category, Long> {
    @Query("SELECT c FROM Category c WHERE c.name LIKE %:keyword% OR c.description LIKE %:keyword%")
    List<Category> search(String keyword);

    @Query("SELECT c FROM Category c WHERE c.deleted = 0 AND c.status = 1 ORDER BY c.sort ASC")
    List<Category> findAllActive();

    boolean existsByName(String name);

    @Query("SELECT COUNT(p) > 0 FROM Product p WHERE p.category.id = :categoryId")
    boolean hasProducts(Long categoryId);
} 