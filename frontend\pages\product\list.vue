<template>
  <view class="container">
    <!-- 搜索栏 -->
    <view class="search-box">
      <input 
        type="text" 
        v-model="searchKeyword" 
        placeholder="搜索产品" 
        class="search-input"
        @confirm="handleSearch"
      />
      <button class="search-btn" @tap="handleSearch">搜索</button>
    </view>

    <!-- 分类标签 -->
    <scroll-view scroll-x class="category-scroll">
      <view class="category-list">
        <view 
          v-for="(item, index) in categories" 
          :key="index"
          :class="['category-item', selectedCategory === item.id ? 'active' : '']"
          @tap="selectCategory(item.id)"
        >
          {{ item.name }}
        </view>
      </view>
    </scroll-view>

    <!-- 产品列表 -->
    <view class="product-list">
      <view 
        class="product-item" 
        v-for="(item, index) in filteredProducts" 
        :key="index"
        @tap="goToDetail(item.id)"
      >
        <image :src="item.image" mode="aspectFill" class="product-image"/>
        <view class="product-info">
          <text class="product-name">{{ item.name }}</text>
          <text class="product-desc">{{ item.description }}</text>
          <view class="product-price">
            <text class="price">¥{{ item.price }}</text>
            <text class="unit">/{{ item.unit }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      searchKeyword: '',
      selectedCategory: 'all',
      categories: [
        { id: 'all', name: '全部' },
        { id: 'camera', name: '监控' },
        { id: 'lock', name: '门锁' },
        { id: 'router', name: '路由器' }
      ],
      products: [
        {
          id: 1,
          name: '智能监控摄像头',
          description: '1080P高清画质，360°全景监控',
          price: 299,
          unit: '台',
          category: 'camera',
          image: '/static/product/camera.svg'
        },
        {
          id: 2,
          name: '智能指纹锁',
          description: '指纹识别，密码解锁，远程控制',
          price: 999,
          unit: '套',
          category: 'lock',
          image: '/static/product/lock.svg'
        },
        {
          id: 3,
          name: 'WiFi6路由器',
          description: '双频千兆，信号增强',
          price: 399,
          unit: '台',
          category: 'router',
          image: '/static/product/router.svg'
        }
      ]
    }
  },
  computed: {
    filteredProducts() {
      let result = this.products
      
      // 分类筛选
      if (this.selectedCategory !== 'all') {
        result = result.filter(item => item.category === this.selectedCategory)
      }
      
      // 关键词搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        result = result.filter(item => 
          item.name.toLowerCase().includes(keyword) || 
          item.description.toLowerCase().includes(keyword)
        )
      }
      
      return result
    }
  },
  methods: {
    handleSearch() {
      // 搜索逻辑已通过计算属性实现
    },
    selectCategory(categoryId) {
      this.selectedCategory = categoryId
    },
    goToDetail(id) {
      uni.navigateTo({
        url: `/pages/product/detail?id=${id}`
      })
    }
  }
}
</script>

<style>
.container {
  padding: 20rpx;
}

.search-box {
  display: flex;
  margin-bottom: 20rpx;
}

.search-input {
  flex: 1;
  height: 72rpx;
  background: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
}

.search-btn {
  width: 120rpx;
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  background: #2979ff;
  color: #fff;
  border-radius: 36rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
}

.category-scroll {
  white-space: nowrap;
  margin-bottom: 20rpx;
}

.category-list {
  display: inline-block;
  padding: 0 20rpx;
}

.category-item {
  display: inline-block;
  padding: 16rpx 32rpx;
  margin-right: 20rpx;
  background: #f5f5f5;
  border-radius: 32rpx;
  font-size: 28rpx;
  color: #666;
}

.category-item.active {
  background: #2979ff;
  color: #fff;
}

.product-list {
  display: flex;
  flex-direction: column;
}

.product-item {
  display: flex;
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.product-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.product-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.product-price {
  margin-top: auto;
}

.price {
  font-size: 36rpx;
  color: #ff6b6b;
  font-weight: bold;
}

.unit {
  font-size: 24rpx;
  color: #999;
  margin-left: 4rpx;
}
</style> 