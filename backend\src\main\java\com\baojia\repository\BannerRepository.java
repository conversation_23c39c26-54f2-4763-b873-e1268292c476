package com.baojia.repository;

import com.baojia.entity.Banner;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import java.util.List;

public interface BannerRepository extends JpaRepository<Banner, Long> {
    @Query("SELECT b FROM Banner b WHERE b.deleted = 0 AND b.status = 1 ORDER BY b.sort ASC")
    List<Banner> findAllActive();
} 