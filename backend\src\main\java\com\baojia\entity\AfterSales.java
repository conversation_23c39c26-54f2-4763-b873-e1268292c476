package com.baojia.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("after_sales")
public class AfterSales {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long userId;
    
    private String serviceType; // 服务类型
    
    private String description; // 问题描述
    
    private String contactPhone; // 联系电话
    
    private String status; // 状态：待处理、处理中、已完成
    
    private String servicePhone; // 售后电话
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    @TableLogic
    private Integer deleted;
} 