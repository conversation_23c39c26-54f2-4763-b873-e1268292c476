"use strict";
const common_vendor = require("../../../common/vendor.js");
const api_product = require("../../../api/product.js");
const api_category = require("../../../api/category.js");
const _sfc_main = {
  data() {
    return {
      id: null,
      categories: [],
      categoryIndex: 0,
      form: {
        name: "",
        categoryId: null,
        price: "",
        stock: "",
        description: "",
        images: [],
        enabled: true
      }
    };
  },
  onLoad(options) {
    if (options.id) {
      this.id = options.id;
      this.loadProduct();
    }
    this.loadCategories();
  },
  methods: {
    async loadCategories() {
      try {
        const res = await api_category.getCategoryList();
        this.categories = res.data;
      } catch (error) {
        common_vendor.index.showToast({
          title: "加载分类列表失败",
          icon: "none"
        });
      }
    },
    async loadProduct() {
      try {
        const res = await api_product.getProductDetail(this.id);
        this.form = res.data;
        this.categoryIndex = this.categories.findIndex(
          (item) => item.id === this.form.categoryId
        );
      } catch (error) {
        common_vendor.index.showToast({
          title: "加载产品信息失败",
          icon: "none"
        });
      }
    },
    handleCategoryChange(e) {
      this.categoryIndex = e.detail.value;
      this.form.categoryId = this.categories[this.categoryIndex].id;
    },
    handleStatusChange(e) {
      this.form.enabled = e.detail.value;
    },
    async handleChooseImage() {
      try {
        const [error, res] = await common_vendor.index.chooseImage({
          count: 9 - this.form.images.length,
          sizeType: ["compressed"],
          sourceType: ["album", "camera"]
        });
        if (error) {
          throw new Error("选择图片失败");
        }
        for (let file of res.tempFilePaths) {
          const fileExt = file.split(".").pop().toLowerCase();
          if (!["png", "jpg", "jpeg"].includes(fileExt)) {
            common_vendor.index.showToast({
              title: "只支持PNG、JPG格式图片",
              icon: "none"
            });
            return;
          }
        }
        for (let file of res.tempFilePaths) {
          const uploadRes = await api_product.uploadProductImage(file);
          this.form.images.push(uploadRes.data);
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: error.message || "上传图片失败",
          icon: "none"
        });
      }
    },
    handleDeleteImage(index) {
      this.form.images.splice(index, 1);
    },
    async handleSave() {
      if (!this.form.name) {
        common_vendor.index.showToast({
          title: "请输入产品名称",
          icon: "none"
        });
        return;
      }
      if (!this.form.categoryId) {
        common_vendor.index.showToast({
          title: "请选择产品分类",
          icon: "none"
        });
        return;
      }
      if (!this.form.price) {
        common_vendor.index.showToast({
          title: "请输入产品价格",
          icon: "none"
        });
        return;
      }
      if (!this.form.stock) {
        common_vendor.index.showToast({
          title: "请输入库存数量",
          icon: "none"
        });
        return;
      }
      try {
        if (this.id) {
          await api_product.updateProduct(this.id, this.form);
        } else {
          await api_product.createProduct(this.form);
        }
        common_vendor.index.showToast({
          title: "保存成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      } catch (error) {
        common_vendor.index.showToast({
          title: error.message || "保存失败",
          icon: "none"
        });
      }
    },
    handleCancel() {
      common_vendor.index.navigateBack();
    },
    async handleUpload() {
      try {
        const [tempFile] = await common_vendor.index.chooseImage({
          count: 1,
          sizeType: ["compressed"],
          sourceType: ["album", "camera"]
        });
        const fileExt = tempFile.tempFilePaths[0].split(".").pop().toLowerCase();
        if (!["png", "jpg", "jpeg"].includes(fileExt)) {
          common_vendor.index.showToast({
            title: "仅支持png、jpg格式图片",
            icon: "none"
          });
          return;
        }
        const res = await uploadFile(tempFile.tempFilePaths[0]);
        this.form.imageUrl = res.data;
      } catch (error) {
        common_vendor.index.showToast({
          title: error.message || "上传失败",
          icon: "none"
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.form.name,
    b: common_vendor.o(($event) => $data.form.name = $event.detail.value),
    c: common_vendor.t($data.form.categoryId ? $data.categories[$data.categoryIndex].name : "请选择分类"),
    d: $data.categories,
    e: $data.categoryIndex,
    f: common_vendor.o((...args) => $options.handleCategoryChange && $options.handleCategoryChange(...args)),
    g: $data.form.price,
    h: common_vendor.o(($event) => $data.form.price = $event.detail.value),
    i: $data.form.stock,
    j: common_vendor.o(($event) => $data.form.stock = $event.detail.value),
    k: $data.form.description,
    l: common_vendor.o(($event) => $data.form.description = $event.detail.value),
    m: common_vendor.f($data.form.images, (image, index, i0) => {
      return {
        a: image,
        b: common_vendor.o(($event) => $options.handleDeleteImage(index), index),
        c: index
      };
    }),
    n: $data.form.images.length < 9
  }, $data.form.images.length < 9 ? {
    o: common_vendor.o((...args) => $options.handleChooseImage && $options.handleChooseImage(...args))
  } : {}, {
    p: $data.form.enabled,
    q: common_vendor.o((...args) => $options.handleStatusChange && $options.handleStatusChange(...args)),
    r: common_vendor.o((...args) => $options.handleSave && $options.handleSave(...args)),
    s: common_vendor.o((...args) => $options.handleCancel && $options.handleCancel(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/admin/product/edit.js.map
