package com.baojia.controller;

import com.baojia.model.Result;
import com.baojia.service.FileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/files")
public class FileController {
    @Autowired
    private FileService fileService;

    @PostMapping("/upload")
    public Result<String> upload(@RequestParam("file") MultipartFile file) {
        String filename = fileService.upload(file);
        return Result.success(filename);
    }

    @DeleteMapping("/{filename}")
    public Result<Void> delete(@PathVariable String filename) {
        fileService.delete(filename);
        return Result.success();
    }
} 