package com.baojia.repository;

import com.baojia.entity.Order;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import java.util.List;

public interface OrderRepository extends JpaRepository<Order, Long> {
    @Query("SELECT o FROM Order o WHERE o.orderNo LIKE %:keyword% OR o.user.phone LIKE %:keyword%")
    List<Order> search(String keyword);

    List<Order> findByStatus(String status);

    @Query("SELECT o FROM Order o WHERE o.orderNo LIKE %:keyword% OR o.user.phone LIKE %:keyword% AND o.status = :status")
    List<Order> searchByStatus(String keyword, String status);
} 