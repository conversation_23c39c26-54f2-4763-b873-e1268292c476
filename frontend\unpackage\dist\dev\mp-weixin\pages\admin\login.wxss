/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}
.login-container .login-box {
  width: 600rpx;
  padding: 40rpx;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.1);
}
.login-container .login-box .title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
}
.login-container .login-box .form .form-item {
  margin-bottom: 20rpx;
}
.login-container .login-box .form .form-item input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #ddd;
  border-radius: 10rpx;
  font-size: 28rpx;
}
.login-container .login-box .form .login-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #007AFF;
  color: #fff;
  font-size: 32rpx;
  border-radius: 10rpx;
  margin-top: 40rpx;
}
.login-container .login-box .wechat-login {
  margin-top: 40rpx;
}
.login-container .login-box .wechat-login .divider {
  position: relative;
  text-align: center;
  margin: 20rpx 0;
}
.login-container .login-box .wechat-login .divider::before, .login-container .login-box .wechat-login .divider::after {
  content: "";
  position: absolute;
  top: 50%;
  width: 30%;
  height: 2rpx;
  background-color: #ddd;
}
.login-container .login-box .wechat-login .divider::before {
  left: 0;
}
.login-container .login-box .wechat-login .divider::after {
  right: 0;
}
.login-container .login-box .wechat-login .divider text {
  display: inline-block;
  padding: 0 20rpx;
  color: #999;
  font-size: 24rpx;
  background-color: #fff;
}
.login-container .login-box .wechat-login .wechat-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #07C160;
  color: #fff;
  font-size: 32rpx;
  border-radius: 10rpx;
}
.login-container .login-box .wechat-login .wechat-btn .iconfont {
  margin-right: 10rpx;
}