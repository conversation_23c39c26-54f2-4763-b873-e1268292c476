package com.baojia.entity;

import lombok.Data;
import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Entity
@Table(name = "product")
public class Product {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String name;

    @ManyToOne
    @JoinColumn(name = "category_id", nullable = false)
    private Category category;

    @Column(columnDefinition = "TEXT")
    private String description;

    @Column(nullable = false)
    private BigDecimal price;

    @Column(nullable = false)
    private Integer stock;

    @Column(nullable = false)
    private Integer sales;

    @Column(name = "main_image", nullable = false)
    private String mainImage;

    @Column(nullable = false)
    private Integer status;

    @Column(nullable = false)
    private Integer sort;

    @Column(name = "is_hot", nullable = false)
    private Integer isHot;

    @Column(name = "is_new", nullable = false)
    private Integer isNew;

    @Column(name = "is_recommend", nullable = false)
    private Integer isRecommend;

    @Column(name = "resolution")
    private String resolution;

    @Column(name = "night_vision")
    private String nightVision;

    @Column(name = "storage")
    private String storage;

    @Column(name = "connection")
    private String connection;

    @Column(name = "create_time", nullable = false, updatable = false)
    private LocalDateTime createTime;

    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;

    @Column(nullable = false)
    private Integer deleted;

    @PrePersist
    public void prePersist() {
        if (createTime == null) {
            createTime = LocalDateTime.now();
        }
        if (updateTime == null) {
            updateTime = LocalDateTime.now();
        }
        if (deleted == null) {
            deleted = 0;
        }
        if (sales == null) {
            sales = 0;
        }
        if (sort == null) {
            sort = 0;
        }
        if (isHot == null) {
            isHot = 0;
        }
        if (isNew == null) {
            isNew = 0;
        }
        if (isRecommend == null) {
            isRecommend = 0;
        }
    }

    @PreUpdate
    public void preUpdate() {
        updateTime = LocalDateTime.now();
    }
} 