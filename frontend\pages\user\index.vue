<template>
  <view class="user-container">
    <!-- 用户信息 -->
    <view class="user-info" @click="handleUserInfoClick">
      <image class="avatar" :src="userInfo.avatar || '/static/default-avatar.png'" mode="aspectFill" />
      <view class="info">
        <text class="nickname">{{ userInfo.nickname || '点击登录' }}</text>
        <text class="phone" v-if="userInfo.phone">{{ userInfo.phone }}</text>
      </view>
    </view>
    
    <!-- 功能菜单 -->
    <view class="menu-list">
      <view class="menu-item" @click="checkLogin('/pages/after-sales/index')">
        <text class="icon">🛠️</text>
        <text>售后服务</text>
      </view>
      <view class="menu-item" @click="handleContact">
        <text class="icon">💬</text>
        <text>联系客服</text>
      </view>
      <!-- 管理员入口 -->
      <view class="menu-item" @click="navigateTo('/pages/admin/index')" v-if="userInfo.userType === 2">
        <text class="icon">⚙️</text>
        <text>后台管理</text>
      </view>
    </view>
  </view>
</template>

<script>
import { getCurrentUser } from '@/api/user'

export default {
  data() {
    return {
      userInfo: {},
      isAdmin: false
    }
  },
  onLoad() {
    this.getUserInfo()
  },
  onShow() {
    // 每次显示页面时重新获取用户信息
    this.getUserInfo()
  },
  methods: {
    async getUserInfo() {
      const token = uni.getStorageSync('token')
      if (token) {
        try {
          // 从服务器获取最新的用户信息
          const userInfo = await getCurrentUser()
          this.userInfo = userInfo
          // 更新本地存储的用户信息
          uni.setStorageSync('userInfo', userInfo)
          // 检查是否是管理员
          this.isAdmin = userInfo.isAdmin || false
        } catch (error) {
          console.error('获取用户信息失败:', error)
          this.userInfo = {}
          uni.removeStorageSync('userInfo')
          uni.removeStorageSync('token')
          this.isAdmin = false
        }
      } else {
        this.userInfo = {}
        this.isAdmin = false
      }
    },
    handleUserInfoClick() {
      if (!this.userInfo.id) {
        this.navigateTo('/pages/login/index')
      }
    },
    checkLogin(url) {
      if (!this.userInfo.id) {
        uni.showModal({
          title: '提示',
          content: '请先登录',
          confirmText: '去登录',
          success: (res) => {
            if (res.confirm) {
              this.navigateTo('/pages/login/index')
            }
          }
        })
        return
      }
      this.navigateTo(url)
    },
    navigateTo(url) {
      uni.navigateTo({ url })
    },
    handleContact() {
      // 打开客服会话
      uni.openCustomerServiceChat({
        extInfo: { url: 'https://work.weixin.qq.com/kfid/xxx' },
        corpId: 'xxx',
        success(res) {
          console.log('打开客服会话成功', res)
        },
        fail(err) {
          console.error('打开客服会话失败', err)
          uni.showToast({
            title: '打开客服会话失败',
            icon: 'none'
          })
        }
      })
    }
  }
}
</script>

<style lang="scss">
.user-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  
  .user-info {
    background-color: #2979ff;
    padding: 40rpx;
    display: flex;
    align-items: center;
    
    .avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      margin-right: 30rpx;
      border: 4rpx solid rgba(255, 255, 255, 0.3);
    }
    
    .info {
      color: #fff;
      
      .nickname {
        font-size: 36rpx;
        font-weight: bold;
        margin-bottom: 10rpx;
        display: block;
      }
      
      .phone {
        font-size: 28rpx;
        opacity: 0.8;
      }
    }
  }
  
  .menu-list {
    padding: 20rpx;
    
    .menu-item {
      background-color: #fff;
      padding: 30rpx;
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;
      
      .icon {
        font-size: 40rpx;
        margin-right: 20rpx;
      }
      
      text {
        font-size: 28rpx;
        color: #333;
      }
    }
  }
}
</style> 