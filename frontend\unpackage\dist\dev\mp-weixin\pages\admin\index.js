"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {};
  },
  onLoad() {
    const token = common_vendor.index.getStorageSync("admin_token");
    if (!token) {
      common_vendor.index.redirectTo({
        url: "/pages/admin/login"
      });
    }
  },
  methods: {
    handleLogout() {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要退出登录吗？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.removeStorageSync("admin_token");
            common_vendor.index.redirectTo({
              url: "/pages/admin/login"
            });
          }
        }
      });
    },
    goToPage(url) {
      common_vendor.index.navigateTo({
        url
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.handleLogout && $options.handleLogout(...args)),
    b: common_vendor.o(($event) => $options.goToPage("/pages/admin/product/list")),
    c: common_vendor.o(($event) => $options.goToPage("/pages/admin/category/list")),
    d: common_vendor.o(($event) => $options.goToPage("/pages/admin/order/list"))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/admin/index.js.map
