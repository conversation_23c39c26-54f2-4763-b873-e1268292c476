"use strict";
const common_vendor = require("../../../common/vendor.js");
const api_admin = require("../../../api/admin.js");
const _sfc_main = {
  data() {
    return {
      searchKeyword: "",
      statusOptions: [
        { value: "", label: "全部状态" },
        { value: "pending", label: "待处理" },
        { value: "processing", label: "处理中" },
        { value: "completed", label: "已完成" },
        { value: "cancelled", label: "已取消" }
      ],
      statusIndex: 0,
      orders: []
    };
  },
  onLoad() {
    this.loadOrders();
  },
  methods: {
    loadOrders() {
      api_admin.getOrders({
        keyword: this.searchKeyword,
        status: this.statusOptions[this.statusIndex].value
      }).then((res) => {
        this.orders = res.data;
      });
    },
    handleSearch() {
      this.loadOrders();
    },
    handleStatusChange(e) {
      this.statusIndex = e.detail.value;
      this.loadOrders();
    },
    getStatusText(status) {
      const statusMap = {
        pending: "待处理",
        processing: "处理中",
        completed: "已完成",
        cancelled: "已取消"
      };
      return statusMap[status] || status;
    },
    goToDetail(id) {
      common_vendor.index.navigateTo({
        url: `/pages/admin/order/detail?id=${id}`
      });
    },
    handleUpdateStatus(id, status) {
      common_vendor.index.showModal({
        title: "提示",
        content: `确定要${this.getStatusText(status)}该订单吗？`,
        success: (res) => {
          if (res.confirm) {
            api_admin.updateOrderStatus(id, { status }).then(() => {
              common_vendor.index.showToast({
                title: "更新成功"
              });
              this.loadOrders();
            });
          }
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    b: $data.searchKeyword,
    c: common_vendor.o(($event) => $data.searchKeyword = $event.detail.value),
    d: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    e: common_vendor.t($data.statusOptions[$data.statusIndex].label),
    f: $data.statusOptions,
    g: $data.statusIndex,
    h: common_vendor.o((...args) => $options.handleStatusChange && $options.handleStatusChange(...args)),
    i: common_vendor.f($data.orders, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.orderNo),
        b: common_vendor.t($options.getStatusText(item.status)),
        c: common_vendor.n(item.status),
        d: item.productImage,
        e: common_vendor.t(item.productName),
        f: common_vendor.t(item.productSpec),
        g: common_vendor.t(item.price),
        h: common_vendor.t(item.createTime),
        i: common_vendor.t(item.username),
        j: common_vendor.t(item.phone),
        k: common_vendor.t(item.totalPrice),
        l: item.status === "pending"
      }, item.status === "pending" ? {
        m: common_vendor.o(($event) => $options.handleUpdateStatus(item.id, "processing"), index)
      } : {}, {
        n: item.status === "processing"
      }, item.status === "processing" ? {
        o: common_vendor.o(($event) => $options.handleUpdateStatus(item.id, "completed"), index)
      } : {}, {
        p: item.status === "pending"
      }, item.status === "pending" ? {
        q: common_vendor.o(($event) => $options.handleUpdateStatus(item.id, "cancelled"), index)
      } : {}, {
        r: index,
        s: common_vendor.o(($event) => $options.goToDetail(item.id), index)
      });
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/admin/order/list.js.map
