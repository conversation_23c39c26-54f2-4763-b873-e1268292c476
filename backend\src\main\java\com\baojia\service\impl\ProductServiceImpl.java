package com.baojia.service.impl;

import com.baojia.entity.Product;
import com.baojia.repository.ProductRepository;
import com.baojia.service.ProductService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class ProductServiceImpl implements ProductService {
    private static final Logger logger = LoggerFactory.getLogger(ProductServiceImpl.class);

    @Autowired
    private ProductRepository productRepository;

    @Override
    @Cacheable(value = "products", key = "'list:' + #keyword + ':' + #categoryId + ':' + #isRecommend")
    public List<Product> list(String keyword, Long categoryId, Boolean isRecommend) {
        logger.info("获取产品列表 - keyword: {}, categoryId: {}, isRecommend: {}", keyword, categoryId, isRecommend);
        List<Product> products;
        if (isRecommend != null && isRecommend) {
            products = productRepository.findAllRecommended();
            logger.info("获取推荐产品 - 数量: {}", products.size());
            return products;
        }
        if (categoryId != null) {
            products = productRepository.findByCategoryId(categoryId);
            logger.info("获取分类产品 - categoryId: {}, 数量: {}", categoryId, products.size());
            return products;
        }
        if (keyword == null || keyword.trim().isEmpty()) {
            products = productRepository.findAllActive();
            logger.info("获取所有激活产品 - 数量: {}", products.size());
            return products;
        }
        products = productRepository.search(keyword);
        logger.info("搜索产品 - keyword: {}, 数量: {}", keyword, products.size());
        return products;
    }

    @Override
    @Cacheable(value = "products", key = "#id")
    public Product getById(Long id) {
        return productRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("产品不存在"));
    }

    @Override
    @Transactional
    @CacheEvict(value = "products", allEntries = true)
    public Product create(Product product) {
        return productRepository.save(product);
    }

    @Override
    @Transactional
    @CacheEvict(value = "products", allEntries = true)
    public Product update(Long id, Product product) {
        Product existingProduct = getById(id);
        existingProduct.setName(product.getName());
        existingProduct.setCategory(product.getCategory());
        existingProduct.setDescription(product.getDescription());
        existingProduct.setPrice(product.getPrice());
        existingProduct.setStock(product.getStock());
        existingProduct.setSales(product.getSales());
        existingProduct.setMainImage(product.getMainImage());
        existingProduct.setStatus(product.getStatus());
        existingProduct.setSort(product.getSort());
        existingProduct.setIsHot(product.getIsHot());
        existingProduct.setIsNew(product.getIsNew());
        existingProduct.setIsRecommend(product.getIsRecommend());
        existingProduct.setResolution(product.getResolution());
        existingProduct.setNightVision(product.getNightVision());
        existingProduct.setStorage(product.getStorage());
        existingProduct.setConnection(product.getConnection());
        return productRepository.save(existingProduct);
    }

    @Override
    @Transactional
    @CacheEvict(value = "products", allEntries = true)
    public void delete(Long id) {
        productRepository.deleteById(id);
    }
} 