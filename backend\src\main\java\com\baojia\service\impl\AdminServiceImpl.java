package com.baojia.service.impl;

import com.baojia.entity.Admin;
import com.baojia.exception.AuthException;
import com.baojia.exception.BusinessException;
import com.baojia.model.LoginRequest;
import com.baojia.model.WechatUserInfo;
import com.baojia.repository.AdminRepository;
import com.baojia.service.AdminService;
import com.baojia.service.WechatService;
import com.baojia.util.JwtUtil;
import com.baojia.util.Validator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.security.core.context.SecurityContextHolder;

@Service
public class AdminServiceImpl implements AdminService {
    @Autowired
    private AdminRepository adminRepository;

    @Autowired
    private JwtUtil jwtUtil;
    
    @Autowired
    private WechatService wechatService;

    @Override
    public String login(LoginRequest request) {
        Admin admin = adminRepository.findByPhone(request.getPhone())
                .orElseThrow(() -> new BusinessException("管理员不存在"));
        
        if (!request.getPassword().equals(admin.getPassword())) {
            throw new BusinessException("密码错误");
        }
        
        return jwtUtil.generateToken(admin.getPhone());
    }

    @Override
    public String register(LoginRequest request) {
        if (adminRepository.existsByPhone(request.getPhone())) {
            throw new BusinessException("手机号已注册");
        }
        
        Admin newAdmin = new Admin();
        newAdmin.setPhone(request.getPhone());
        newAdmin.setPassword(request.getPassword());
        newAdmin.setStatus(1);
        newAdmin.setDeleted(0);
        
        adminRepository.save(newAdmin);
        return jwtUtil.generateToken(newAdmin.getPhone());
    }

    @Override
    public Admin getCurrentAdmin() {
        String token = SecurityContextHolder.getContext().getAuthentication().getCredentials().toString();
        String phone = jwtUtil.getPhoneFromToken(token);
        return adminRepository.findByPhone(phone)
                .orElseThrow(() -> new AuthException("管理员不存在"));
    }

    @Override
    public void sendCode(LoginRequest request) {
        Validator.notEmpty(request.getPhone(), "手机号不能为空");
        // TODO: 实现发送验证码逻辑
    }

    @Override
    public Object getInfo(String token) {
        String phone = jwtUtil.getPhoneFromToken(token);
        return adminRepository.findByPhone(phone)
                .orElseThrow(() -> new AuthException("管理员不存在"));
    }

    @Override
    public void update(String token, LoginRequest request) {
        String phone = jwtUtil.getPhoneFromToken(token);
        Admin admin = adminRepository.findByPhone(phone)
                .orElseThrow(() -> new AuthException("管理员不存在"));
        
        if (request.getPassword() != null) {
            admin.setPassword(request.getPassword());
        }
        
        adminRepository.save(admin);
    }

    @Override
    @Transactional
    public Admin create(Admin admin) {
        Validator.notNull(admin, "管理员信息不能为空");
        Validator.notEmpty(admin.getPhone(), "手机号不能为空");
        Validator.notEmpty(admin.getPassword(), "密码不能为空");

        if (adminRepository.existsByPhone(admin.getPhone())) {
            throw new BusinessException("手机号已存在");
        }

        return adminRepository.save(admin);
    }

    @Override
    @Transactional
    public Admin update(Long id, Admin admin) {
        Validator.notNull(id, "管理员ID不能为空");
        Validator.notNull(admin, "管理员信息不能为空");
        Validator.notEmpty(admin.getPhone(), "手机号不能为空");

        Admin existingAdmin = adminRepository.findById(id)
                .orElseThrow(() -> new BusinessException("管理员不存在"));

        if (!existingAdmin.getPhone().equals(admin.getPhone()) 
            && adminRepository.existsByPhone(admin.getPhone())) {
            throw new BusinessException("手机号已存在");
        }

        existingAdmin.setPhone(admin.getPhone());
        if (admin.getPassword() != null && !admin.getPassword().isEmpty()) {
            existingAdmin.setPassword(admin.getPassword());
        }
        return adminRepository.save(existingAdmin);
    }
    
    @Override
    @Transactional
    public Admin bindPhone(String phone, String password) {
        Validator.notEmpty(phone, "手机号不能为空");
        Validator.notEmpty(password, "密码不能为空");
        
        Admin admin = getCurrentAdmin();
        
        if (adminRepository.existsByPhone(phone)) {
            throw new BusinessException("该手机号已被使用");
        }
        
        admin.setPhone(phone);
        admin.setPassword(password);
        
        return adminRepository.save(admin);
    }

    private String loginByWechat(String code) {
        // 获取微信用户信息
        WechatUserInfo wechatUserInfo = wechatService.getUserInfo(code);
        
        // 查找或创建管理员账号
        Admin admin = adminRepository.findByWechatOpenid(wechatUserInfo.getOpenid())
                .orElseGet(() -> {
                    // 如果是新用户，自动创建账号
                    Admin newAdmin = new Admin();
                    newAdmin.setWechatOpenid(wechatUserInfo.getOpenid());
                    newAdmin.setWechatUnionid(wechatUserInfo.getUnionid());
                    newAdmin.setWechatNickname(wechatUserInfo.getNickname());
                    newAdmin.setWechatAvatar(wechatUserInfo.getAvatar());
                    newAdmin.setPhone(""); // 可以后续绑定手机号
                    newAdmin.setPassword(""); // 设置空密码
                    return adminRepository.save(newAdmin);
                });

        return jwtUtil.generateToken(admin.getPhone());
    }
} 