import request from '@/utils/request'

// 管理员登录
export function login(data) {
  return request({
    url: '/api/admin/login',
    method: 'post',
    data: {
      phone: data.phone,
      password: data.password
    }
  })
}

// 获取管理员信息
export function getInfo() {
  return request({
    url: '/api/admin/info',
    method: 'get'
  })
}

// 退出登录
export function logout() {
  return request({
    url: '/api/admin/logout',
    method: 'post'
  })
}

// 获取分类列表
export function getCategories(keyword) {
  return request({
    url: '/api/categories',
    method: 'GET',
    params: { keyword }
  })
}

// 获取分类详情
export function getCategory(id) {
  return request({
    url: `/api/categories/${id}`,
    method: 'GET'
  })
}

// 创建分类
export function createCategory(data) {
  return request({
    url: '/api/categories',
    method: 'POST',
    data
  })
}

// 更新分类
export function updateCategory(id, data) {
  return request({
    url: `/api/categories/${id}`,
    method: 'PUT',
    data
  })
}

// 删除分类
export function deleteCategory(id) {
  return request({
    url: `/api/categories/${id}`,
    method: 'DELETE'
  })
}

// 获取产品列表
export function getProducts(keyword, categoryId) {
  return request({
    url: '/api/products',
    method: 'GET',
    data: { keyword, categoryId }
  })
}

// 获取产品详情
export function getProduct(id) {
  return request({
    url: `/api/products/${id}`,
    method: 'GET'
  })
}

// 创建产品
export function createProduct(data) {
  return request({
    url: '/api/products',
    method: 'POST',
    data
  })
}

// 更新产品
export function updateProduct(id, data) {
  return request({
    url: `/admin/products/${id}`,
    method: 'PUT',
    data
  })
}

// 删除产品
export function deleteProduct(id) {
  return request({
    url: `/api/products/${id}`,
    method: 'DELETE'
  })
}

// 获取订单列表
export function getOrders(keyword, status) {
  return request({
    url: '/admin/orders',
    method: 'GET',
    data: { keyword, status }
  })
}

// 获取订单详情
export function getOrder(id) {
  return request({
    url: `/admin/orders/${id}`,
    method: 'GET'
  })
}

// 更新订单状态
export function updateOrderStatus(id, status, handleRemark) {
  return request({
    url: `/admin/orders/${id}/status`,
    method: 'PUT',
    data: { status, handleRemark }
  })
}

// 上传文件
export function uploadFile(file) {
  return request({
    url: '/admin/files/upload',
    method: 'POST',
    header: {
      'Content-Type': 'multipart/form-data'
    },
    data: file
  })
}

// 删除文件
export function deleteFile(filename) {
  return request({
    url: `/admin/files/${filename}`,
    method: 'DELETE'
  })
} 