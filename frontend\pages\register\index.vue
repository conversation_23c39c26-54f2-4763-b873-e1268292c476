<template>
  <view class="register-container">
    <view class="header">
      <text class="title">注册账号</text>
    </view>
    
    <view class="form">
      <view class="input-group">
        <text class="label">手机号</text>
        <input 
          type="number" 
          v-model="form.phone" 
          placeholder="请输入手机号"
          maxlength="11"
        />
      </view>
      
      <view class="input-group">
        <text class="label">验证码</text>
        <input 
          type="number" 
          v-model="form.code" 
          placeholder="请输入验证码"
          maxlength="6"
        />
        <text 
          class="send-code" 
          :class="{ disabled: counting }"
          @click="sendCode"
        >{{ counting ? `${countdown}s后重发` : '发送验证码' }}</text>
      </view>
      
      <view class="input-group">
        <text class="label">密码</text>
        <input 
          :type="showPassword ? 'text' : 'password'" 
          v-model="form.password" 
          placeholder="请输入密码"
        />
        <text 
          class="toggle-password" 
          @click="showPassword = !showPassword"
        >{{ showPassword ? '隐藏' : '显示' }}</text>
      </view>
      
      <view class="input-group">
        <text class="label">确认密码</text>
        <input 
          :type="showConfirmPassword ? 'text' : 'password'" 
          v-model="form.confirmPassword" 
          placeholder="请再次输入密码"
        />
        <text 
          class="toggle-password" 
          @click="showConfirmPassword = !showConfirmPassword"
        >{{ showConfirmPassword ? '隐藏' : '显示' }}</text>
      </view>
      
      <button 
        class="register-btn" 
        :disabled="!canRegister"
        @click="handleRegister"
      >注册</button>
      
      <view class="other-options">
        <text @click="navigateTo('/pages/login/index')">已有账号？去登录</text>
      </view>
    </view>
  </view>
</template>

<script>
import { register, sendCode } from '@/api/user'

export default {
  data() {
    return {
      form: {
        phone: '',
        code: '',
        password: '',
        confirmPassword: ''
      },
      showPassword: false,
      showConfirmPassword: false,
      counting: false,
      countdown: 60
    }
  },
  computed: {
    canRegister() {
      return this.form.phone.length === 11 && 
             this.form.code.length === 6 && 
             this.form.password.length >= 6 &&
             this.form.password === this.form.confirmPassword
    }
  },
  methods: {
    async sendCode() {
      if (this.counting) return
      if (this.form.phone.length !== 11) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }
      
      try {
        await sendCode(this.form.phone)
        this.counting = true
        this.countdown = 60
        const timer = setInterval(() => {
          this.countdown--
          if (this.countdown <= 0) {
            clearInterval(timer)
            this.counting = false
          }
        }, 1000)
        
        uni.showToast({
          title: '验证码已发送',
          icon: 'success'
        })
      } catch (error) {
        uni.showToast({
          title: '发送失败，请重试',
          icon: 'none'
        })
      }
    },
    async handleRegister() {
      if (!this.canRegister) return
      
      if (this.form.password !== this.form.confirmPassword) {
        uni.showToast({
          title: '两次输入的密码不一致',
          icon: 'none'
        })
        return
      }
      
      try {
        await register({
          phone: this.form.phone,
          password: this.form.password,
          code: this.form.code
        })
        
        uni.showToast({
          title: '注册成功',
          icon: 'success'
        })
        
        // 返回登录页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      } catch (error) {
        uni.showToast({
          title: error.message || '注册失败，请重试',
          icon: 'none'
        })
      }
    },
    navigateTo(url) {
      uni.navigateTo({ url })
    }
  }
}
</script>

<style lang="scss">
.register-container {
  min-height: 100vh;
  padding: 40rpx;
  background-color: #fff;
  
  .header {
    margin: 60rpx 0;
    
    .title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }
  }
  
  .form {
    .input-group {
      position: relative;
      margin-bottom: 30rpx;
      
      .label {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 10rpx;
        display: block;
      }
      
      input {
        width: 100%;
        height: 88rpx;
        background-color: #f5f5f5;
        border-radius: 44rpx;
        padding: 0 40rpx;
        font-size: 28rpx;
      }
      
      .send-code {
        position: absolute;
        right: 40rpx;
        top: 50%;
        transform: translateY(-50%);
        font-size: 28rpx;
        color: #2979ff;
        
        &.disabled {
          color: #999;
        }
      }
      
      .toggle-password {
        position: absolute;
        right: 40rpx;
        top: 50%;
        transform: translateY(-50%);
        font-size: 28rpx;
        color: #666;
      }
    }
    
    .register-btn {
      width: 100%;
      height: 88rpx;
      background-color: #2979ff;
      color: #fff;
      font-size: 32rpx;
      border-radius: 44rpx;
      margin-top: 60rpx;
      
      &[disabled] {
        background-color: #ccc;
      }
    }
    
    .other-options {
      display: flex;
      justify-content: center;
      margin-top: 30rpx;
      
      text {
        font-size: 28rpx;
        color: #666;
      }
    }
  }
}
</style> 