{"version": 3, "file": "index.js", "sources": ["config/index.js"], "sourcesContent": ["// 开发环境配置\r\nconst dev = {\r\n  baseURL: 'http://localhost:8080/api'\r\n}\r\n\r\n// 生产环境配置\r\nconst prod = {\r\n  baseURL: 'https://api.example.com/api' // 替换为实际的生产环境API地址\r\n}\r\n\r\n// 根据环境导出配置\r\nexport default process.env.NODE_ENV === 'development' ? dev : prod\r\n\r\n// 导出baseURL供其他模块使用\r\nexport const baseURL = process.env.NODE_ENV === 'development' ? dev.baseURL : prod.baseURL "], "names": [], "mappings": ";AACA,MAAM,MAAM;AAAA,EACV,SAAS;AACX;AAWO,MAAM,UAAmD,IAAI;;"}