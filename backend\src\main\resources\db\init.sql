-- 创建用户表
CREATE TABLE `user` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `phone` VARCHAR(11) NOT NULL COMMENT '手机号',
    `password` VARCHAR(100) NOT NULL COMMENT '密码',
    `nickname` VARCHAR(50) DEFAULT NULL COMMENT '昵称',
    `real_name` VARCHAR(50) DEFAULT NULL COMMENT '真实姓名',
    `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
    `avatar` VARCHAR(255) DEFAULT NULL COMMENT '头像URL',
    `address` VARCHAR(200) DEFAULT NULL COMMENT '地址',
    `user_type` TINYINT NOT NULL DEFAULT 0 COMMENT '用户类型：0普通用户 1会员',
    `member_level` VARCHAR(20) NOT NULL DEFAULT '普通会员' COMMENT '会员等级',
    `points` INT DEFAULT 0 COMMENT '积分',
    `last_login_time` DATETIME DEFAULT NULL COMMENT '最后登录时间',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：0未删除 1已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_phone` (`phone`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 创建管理员表
CREATE TABLE `admin` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `phone` VARCHAR(11) NOT NULL COMMENT '手机号',
    `password` VARCHAR(100) NOT NULL COMMENT '密码',
    `real_name` VARCHAR(50) DEFAULT NULL COMMENT '真实姓名',
    `role` VARCHAR(20) NOT NULL DEFAULT 'ADMIN' COMMENT '角色：ADMIN普通管理员 SUPER_ADMIN超级管理员',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0禁用 1启用',
    `last_login_time` DATETIME DEFAULT NULL COMMENT '最后登录时间',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：0未删除 1已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_phone` (`phone`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- 创建产品分类表
CREATE TABLE `category` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name` VARCHAR(50) NOT NULL COMMENT '分类名称',
    `sort` INT NOT NULL DEFAULT 0 COMMENT '排序号',
    `icon` VARCHAR(255) DEFAULT NULL COMMENT '分类图标',
    `description` VARCHAR(200) DEFAULT NULL COMMENT '分类描述',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0禁用 1启用',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：0未删除 1已删除',
    PRIMARY KEY (`id`),
    KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品分类表';

-- 创建产品表
CREATE TABLE `product` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `category_id` BIGINT NOT NULL COMMENT '分类ID',
    `name` VARCHAR(100) NOT NULL COMMENT '产品名称',
    `description` TEXT COMMENT '产品描述',
    `price` DECIMAL(10,2) NOT NULL COMMENT '产品价格',
    `stock` INT NOT NULL DEFAULT 0 COMMENT '库存数量',
    `sales` INT NOT NULL DEFAULT 0 COMMENT '销量',
    `main_image` VARCHAR(255) NOT NULL COMMENT '主图URL',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0下架 1上架',
    `sort` INT NOT NULL DEFAULT 0 COMMENT '排序号',
    `is_hot` TINYINT NOT NULL DEFAULT 0 COMMENT '是否热门：0否 1是',
    `is_new` TINYINT NOT NULL DEFAULT 0 COMMENT '是否新品：0否 1是',
    `is_recommend` TINYINT NOT NULL DEFAULT 0 COMMENT '是否推荐：0否 1是',
    `resolution` VARCHAR(50) DEFAULT NULL COMMENT '分辨率',
    `night_vision` VARCHAR(50) DEFAULT NULL COMMENT '夜视功能',
    `storage` VARCHAR(50) DEFAULT NULL COMMENT '存储容量',
    `connection` VARCHAR(50) DEFAULT NULL COMMENT '连接方式',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：0未删除 1已删除',
    PRIMARY KEY (`id`),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_status` (`status`),
    KEY `idx_sort` (`sort`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品表';

-- 创建产品图片表
CREATE TABLE `product_image` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `product_id` BIGINT NOT NULL COMMENT '产品ID',
    `url` VARCHAR(255) NOT NULL COMMENT '图片URL',
    `sort` INT NOT NULL DEFAULT 0 COMMENT '排序号',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：0未删除 1已删除',
    PRIMARY KEY (`id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品图片表';

-- 创建轮播图表
CREATE TABLE IF NOT EXISTS `banner` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `image` varchar(255) NOT NULL COMMENT '图片地址',
  `link` varchar(255) DEFAULT NULL COMMENT '链接地址',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='轮播图表';

-- 插入默认超级管理员
INSERT INTO `admin` (`phone`, `password`, `real_name`, `role`, `status`) 
VALUES ('18636831192', 'admin123', '超级管理员', 'SUPER_ADMIN', 1);

-- 插入测试普通用户
INSERT INTO `user` (`phone`, `password`, `nickname`, `real_name`, `email`, `address`, `user_type`, `member_level`) 
VALUES ('18636831192', 'admin123', '测试用户', '张三', '<EMAIL>', '北京市朝阳区', 2, '管理员');

-- 插入监控产品分类数据
INSERT INTO `category` (`name`, `sort`, `description`, `status`) VALUES 
('网络摄像机', 1, '支持远程监控的网络摄像头', 1),
('监控套装', 2, '包含NVR和多个摄像头的监控套装', 1),
('配件设备', 3, '硬盘、支架等配件', 1),
('智能门铃', 4, '支持视频通话的智能门铃', 1);

-- 插入监控产品数据
INSERT INTO `product` (
    `category_id`, 
    `name`, 
    `description`, 
    `price`, 
    `stock`, 
    `main_image`, 
    `status`, 
    `sort`, 
    `is_hot`, 
    `is_new`, 
    `is_recommend`,
    `resolution`,
    `night_vision`,
    `storage`,
    `connection`
) VALUES 
(1, 'H265 POE摄像机', '支持POE供电，H265编码，红外夜视，IP67防水', 299.00, 100, '/uploads/products/camera1.jpg', 1, 1, 1, 1, 1, '2K超清', '30米红外', '支持最大128G', 'POE/网线'),
(1, '无线球机摄像头', '355度旋转，双向语音，AI人形检测', 199.00, 50, '/uploads/products/camera2.jpg', 1, 2, 1, 1, 1, '1080P高清', '15米红外', '支持最大64G', 'WiFi'),
(2, '8路POE监控套装', '含8路NVR主机+8个POE摄像头+2TB监控硬盘', 1999.00, 30, '/uploads/products/kit1.jpg', 1, 1, 1, 0, 1, '1080P高清', '20米红外', '2TB', 'POE/网线');

-- 插入测试数据
INSERT INTO `banner` (`image`, `link`, `sort`) VALUES
('/static/banner/banner1.jpg', '/pages/product/detail?id=1', 1),
('/static/banner/banner2.jpg', '/pages/product/detail?id=2', 2),
('/static/banner/banner3.jpg', '/pages/product/detail?id=3', 3); 