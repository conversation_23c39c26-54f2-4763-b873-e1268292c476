{"version": 3, "file": "request.js", "sources": ["utils/request.js"], "sourcesContent": ["import { baseURL } from '../config'\r\n\r\n// 请求拦截器\r\nconst request = (options) => {\r\n  // 添加baseURL\r\n  options.url = baseURL + options.url\r\n  \r\n  // 添加token\r\n  const token = uni.getStorageSync('token')\r\n  if (token) {\r\n    options.header = {\r\n      ...options.header,\r\n      'Authorization': token\r\n    }\r\n  }\r\n  \r\n  return new Promise((resolve, reject) => {\r\n    uni.request({\r\n      ...options,\r\n      success: (res) => {\r\n        if (res.statusCode === 200) {\r\n          if (res.data.code === 0) {\r\n            resolve(res.data)\r\n          } else {\r\n            uni.showToast({\r\n              title: res.data.message || '请求失败',\r\n              icon: 'none'\r\n            })\r\n            reject(res.data)\r\n          }\r\n        } else if (res.statusCode === 401) {\r\n          // token过期，清除登录状态\r\n          uni.removeStorageSync('token')\r\n          uni.showToast({\r\n            title: '登录已过期，请重新登录',\r\n            icon: 'none'\r\n          })\r\n          setTimeout(() => {\r\n            uni.reLaunch({\r\n              url: '/pages/admin/login'\r\n            })\r\n          }, 1500)\r\n          reject(res.data)\r\n        } else {\r\n          uni.showToast({\r\n            title: res.data.message || '请求失败',\r\n            icon: 'none'\r\n          })\r\n          reject(res.data)\r\n        }\r\n      },\r\n      fail: (err) => {\r\n        uni.showToast({\r\n          title: '网络请求失败',\r\n          icon: 'none'\r\n        })\r\n        reject(err)\r\n      }\r\n    })\r\n  })\r\n}\r\n\r\nexport default request "], "names": ["baseURL", "uni"], "mappings": ";;;AAGK,MAAC,UAAU,CAAC,YAAY;AAE3B,UAAQ,MAAMA,uBAAU,QAAQ;AAGhC,QAAM,QAAQC,cAAAA,MAAI,eAAe,OAAO;AACxC,MAAI,OAAO;AACT,YAAQ,SAAS;AAAA,MACf,GAAG,QAAQ;AAAA,MACX,iBAAiB;AAAA,IAClB;AAAA,EACF;AAED,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,kBAAAA,MAAI,QAAQ;AAAA,MACV,GAAG;AAAA,MACH,SAAS,CAAC,QAAQ;AAChB,YAAI,IAAI,eAAe,KAAK;AAC1B,cAAI,IAAI,KAAK,SAAS,GAAG;AACvB,oBAAQ,IAAI,IAAI;AAAA,UAC5B,OAAiB;AACLA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO,IAAI,KAAK,WAAW;AAAA,cAC3B,MAAM;AAAA,YACpB,CAAa;AACD,mBAAO,IAAI,IAAI;AAAA,UAChB;AAAA,QACX,WAAmB,IAAI,eAAe,KAAK;AAEjCA,wBAAG,MAAC,kBAAkB,OAAO;AAC7BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAClB,CAAW;AACD,qBAAW,MAAM;AACfA,0BAAAA,MAAI,SAAS;AAAA,cACX,KAAK;AAAA,YACnB,CAAa;AAAA,UACF,GAAE,IAAI;AACP,iBAAO,IAAI,IAAI;AAAA,QACzB,OAAe;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI,KAAK,WAAW;AAAA,YAC3B,MAAM;AAAA,UAClB,CAAW;AACD,iBAAO,IAAI,IAAI;AAAA,QAChB;AAAA,MACF;AAAA,MACD,MAAM,CAAC,QAAQ;AACbA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAChB,CAAS;AACD,eAAO,GAAG;AAAA,MACX;AAAA,IACP,CAAK;AAAA,EACL,CAAG;AACH;;"}