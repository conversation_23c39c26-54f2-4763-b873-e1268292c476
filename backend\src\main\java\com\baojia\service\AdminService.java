package com.baojia.service;

import com.baojia.entity.Admin;
import com.baojia.model.LoginRequest;

public interface AdminService {
    String login(LoginRequest request);
    String register(LoginRequest request);
    void sendCode(LoginRequest request);
    Object getInfo(String token);
    void update(String token, LoginRequest request);
    Admin getCurrentAdmin();
    Admin create(Admin admin);
    Admin update(Long id, Admin admin);
    
    /**
     * 绑定手机号
     * @param phone 手机号
     * @param password 密码
     * @return 更新后的管理员信息
     */
    Admin bindPhone(String phone, String password);
} 