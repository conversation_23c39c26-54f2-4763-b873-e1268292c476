<view class="container"><view class="header"><view class="search-box"><input type="text" placeholder="搜索订单号/用户名" class="search-input" bindconfirm="{{a}}" value="{{b}}" bindinput="{{c}}"/><button class="search-btn" bindtap="{{d}}">搜索</button></view><view class="filter-box"><picker mode="selector" range="{{f}}" range-key="label" value="{{g}}" bindchange="{{h}}" class="status-picker"><view class="picker-text">{{e}}</view></picker></view></view><view class="order-list"><view wx:for="{{i}}" wx:for-item="item" wx:key="r" class="order-item" bindtap="{{item.s}}"><view class="order-header"><text class="order-no">订单号：{{item.a}}</text><text class="{{['order-status', item.c]}}">{{item.b}}</text></view><view class="order-content"><view class="product-info"><image src="{{item.d}}" mode="aspectFill" class="product-image"/><view class="product-detail"><text class="product-name">{{item.e}}</text><text class="product-spec">{{item.f}}</text><text class="product-price">¥{{item.g}}</text></view></view><view class="order-info"><text class="info-item">下单时间：{{item.h}}</text><text class="info-item">用户：{{item.i}}</text><text class="info-item">联系电话：{{item.j}}</text></view></view><view class="order-footer"><text class="total-price">总计：¥{{item.k}}</text><view class="action-btns"><button wx:if="{{item.l}}" class="action-btn" catchtap="{{item.m}}">接单</button><button wx:if="{{item.n}}" class="action-btn" catchtap="{{item.o}}">完成</button><button wx:if="{{item.p}}" class="action-btn cancel" catchtap="{{item.q}}">取消</button></view></view></view></view></view>