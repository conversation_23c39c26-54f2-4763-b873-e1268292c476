package com.baojia.service.impl;

import com.baojia.config.WechatConfig;
import com.baojia.exception.BusinessException;
import com.baojia.model.WechatUserInfo;
import com.baojia.service.WechatService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
public class WechatServiceImpl implements WechatService {
    
    @Autowired
    private WechatConfig wechatConfig;
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    private static final String WECHAT_API_URL = "https://api.weixin.qq.com/sns/oauth2/access_token";
    private static final String WECHAT_USER_INFO_URL = "https://api.weixin.qq.com/sns/userinfo";
    
    @Override
    public WechatUserInfo getUserInfo(String code) {
        try {
            // 1. 获取access_token
            String accessTokenUrl = String.format("%s?appid=%s&secret=%s&code=%s&grant_type=%s",
                    WECHAT_API_URL,
                    wechatConfig.getAppId(),
                    wechatConfig.getAppSecret(),
                    code,
                    wechatConfig.getGrantType());
                    
            String response = restTemplate.getForObject(accessTokenUrl, String.class);
            JsonNode jsonNode = objectMapper.readTree(response);
            
            if (jsonNode.has("errcode")) {
                throw new BusinessException("获取微信access_token失败: " + jsonNode.get("errmsg").asText());
            }
            
            String accessToken = jsonNode.get("access_token").asText();
            String openid = jsonNode.get("openid").asText();
            String unionid = jsonNode.has("unionid") ? jsonNode.get("unionid").asText() : null;
            
            // 2. 获取用户信息
            String userInfoUrl = String.format("%s?access_token=%s&openid=%s",
                    WECHAT_USER_INFO_URL,
                    accessToken,
                    openid);
                    
            response = restTemplate.getForObject(userInfoUrl, String.class);
            jsonNode = objectMapper.readTree(response);
            
            if (jsonNode.has("errcode")) {
                throw new BusinessException("获取微信用户信息失败: " + jsonNode.get("errmsg").asText());
            }
            
            WechatUserInfo userInfo = new WechatUserInfo();
            userInfo.setOpenid(openid);
            userInfo.setUnionid(unionid);
            userInfo.setNickname(jsonNode.get("nickname").asText());
            userInfo.setAvatar(jsonNode.get("headimgurl").asText());
            userInfo.setGender(jsonNode.get("sex").asInt());
            userInfo.setCountry(jsonNode.get("country").asText());
            userInfo.setProvince(jsonNode.get("province").asText());
            userInfo.setCity(jsonNode.get("city").asText());
            
            return userInfo;
            
        } catch (Exception e) {
            throw new BusinessException("微信登录失败: " + e.getMessage());
        }
    }
} 