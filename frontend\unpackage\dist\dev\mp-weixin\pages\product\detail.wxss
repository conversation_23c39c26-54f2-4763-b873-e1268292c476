/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container {
  padding-bottom: 100rpx;
}
.product-info {
  padding: 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}
.product-info .price-box {
  margin-bottom: 20rpx;
}
.product-info .price-box .price {
  font-size: 48rpx;
  color: #ff6b6b;
  font-weight: bold;
}
.product-info .price-box .unit {
  font-size: 24rpx;
  color: #999;
  margin-left: 4rpx;
}
.product-info .name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}
.product-info .desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}
.spec-box {
  padding: 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}
.spec-box .title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.spec-box .spec-list .spec-item {
  display: flex;
  margin-bottom: 20rpx;
}
.spec-box .spec-list .spec-item .label {
  width: 160rpx;
  color: #999;
}
.spec-box .spec-list .spec-item .value {
  flex: 1;
  color: #333;
}
.video-box {
  padding: 30rpx;
  background-color: #fff;
}
.video-box .title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.video-box .video {
  width: 100%;
  height: 400rpx;
}
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.bottom-nav .nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 60rpx;
}
.bottom-nav .nav-item text {
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
}
.bottom-nav .contact-btn {
  flex: 1;
  height: 80rpx;
  background-color: #2979ff;
  color: #fff;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}