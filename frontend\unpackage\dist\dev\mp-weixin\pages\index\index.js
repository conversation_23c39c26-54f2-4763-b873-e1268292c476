"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      banners: [
        {
          image: "/static/product/camera.svg",
          link: "/pages/product/detail?id=1"
        },
        {
          image: "/static/product/lock.svg",
          link: "/pages/product/detail?id=2"
        },
        {
          image: "/static/product/router.svg",
          link: "/pages/product/detail?id=3"
        }
      ],
      categories: [
        {
          id: "camera",
          name: "监控",
          icon: "/static/category/camera.svg"
        },
        {
          id: "lock",
          name: "门锁",
          icon: "/static/category/lock.svg"
        },
        {
          id: "router",
          name: "路由器",
          icon: "/static/category/router.svg"
        }
      ],
      recommendProducts: [
        {
          id: 1,
          name: "智能监控摄像头",
          description: "1080P高清画质，360°全景监控",
          price: 299,
          unit: "台",
          image: "/static/product/camera.svg"
        },
        {
          id: 2,
          name: "智能指纹锁",
          description: "指纹识别，密码解锁，远程控制",
          price: 999,
          unit: "套",
          image: "/static/product/lock.svg"
        },
        {
          id: 3,
          name: "WiFi6路由器",
          description: "双频千兆，信号增强",
          price: 399,
          unit: "台",
          image: "/static/product/router.svg"
        }
      ]
    };
  },
  onLoad() {
  },
  methods: {
    goToCategory(categoryId) {
      common_vendor.index.navigateTo({
        url: `/pages/product/list?category=${categoryId}`
      });
    },
    goToDetail(id) {
      common_vendor.index.navigateTo({
        url: `/pages/product/detail?id=${id}`
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($data.banners, (item, index, i0) => {
      return {
        a: item.image,
        b: index
      };
    }),
    b: common_vendor.f($data.categories, (item, index, i0) => {
      return {
        a: item.icon,
        b: common_vendor.t(item.name),
        c: index,
        d: common_vendor.o(($event) => $options.goToCategory(item.id), index)
      };
    }),
    c: common_vendor.f($data.recommendProducts, (item, index, i0) => {
      return {
        a: item.image,
        b: common_vendor.t(item.name),
        c: common_vendor.t(item.description),
        d: common_vendor.t(item.price),
        e: common_vendor.t(item.unit),
        f: index,
        g: common_vendor.o(($event) => $options.goToDetail(item.id), index)
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
