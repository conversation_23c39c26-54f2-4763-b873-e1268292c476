<view class="container"><view class="search-box"><input type="text" placeholder="搜索产品" class="search-input" bindconfirm="{{a}}" value="{{b}}" bindinput="{{c}}"/><button class="search-btn" bindtap="{{d}}">搜索</button></view><scroll-view scroll-x class="category-scroll"><view class="category-list"><view wx:for="{{e}}" wx:for-item="item" wx:key="b" class="{{['category-item', item.c]}}" bindtap="{{item.d}}">{{item.a}}</view></view></scroll-view><view class="product-list"><view wx:for="{{f}}" wx:for-item="item" wx:key="f" class="product-item" bindtap="{{item.g}}"><image src="{{item.a}}" mode="aspectFill" class="product-image"/><view class="product-info"><text class="product-name">{{item.b}}</text><text class="product-desc">{{item.c}}</text><view class="product-price"><text class="price">¥{{item.d}}</text><text class="unit">/{{item.e}}</text></view></view></view></view></view>