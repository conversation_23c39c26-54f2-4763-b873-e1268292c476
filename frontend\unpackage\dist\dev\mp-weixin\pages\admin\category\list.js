"use strict";
const common_vendor = require("../../../common/vendor.js");
const api_admin = require("../../../api/admin.js");
const _sfc_main = {
  data() {
    return {
      searchKeyword: "",
      categories: []
    };
  },
  onLoad() {
    this.loadCategories();
  },
  methods: {
    loadCategories() {
      api_admin.getCategories({
        keyword: this.searchKeyword
      }).then((res) => {
        this.categories = res.data;
      });
    },
    handleSearch() {
      this.loadCategories();
    },
    handleAdd() {
      common_vendor.index.navigateTo({
        url: "/pages/admin/category/edit"
      });
    },
    handleEdit(id) {
      common_vendor.index.navigateTo({
        url: `/pages/admin/category/edit?id=${id}`
      });
    },
    handleDelete(id) {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要删除该分类吗？",
        success: (res) => {
          if (res.confirm) {
            api_admin.deleteCategory(id).then(() => {
              common_vendor.index.showToast({
                title: "删除成功"
              });
              this.loadCategories();
            });
          }
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    b: $data.searchKeyword,
    c: common_vendor.o(($event) => $data.searchKeyword = $event.detail.value),
    d: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    e: common_vendor.o((...args) => $options.handleAdd && $options.handleAdd(...args)),
    f: common_vendor.f($data.categories, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: common_vendor.t(item.description),
        c: common_vendor.o(($event) => $options.handleEdit(item.id), index),
        d: common_vendor.o(($event) => $options.handleDelete(item.id), index),
        e: index
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/admin/category/list.js.map
